# مرجع مشروع تطبيق إدارة المديونية - فقيه

## نظرة عامة على المشروع

### الهدف الرئيسي
بناء تطبيق خاص لإدارة المديونية للعملاء والموظفين مع إمكانية تصدير تقارير المديونية كملفات PDF في أي وقت.

### اسم المشروع
**فقيه (Faqeh)** - تطبيق Flutter لإدارة المديونية

---

## المتطلبات الوظيفية

### 1. إدارة العملاء والموظفين
- **إضافة عملاء جدد**: تسجيل بيانات العملاء الأساسية
- **إضافة موظفين جدد**: تسجيل بيانات الموظفين الأساسية
- **عرض قائمة العملاء والموظفين**: واجهة لاستعراض جميع المسجلين
- **تعديل البيانات**: إمكانية تحديث معلومات العملاء والموظفين
- **حذف السجلات**: إزالة العملاء أو الموظفين غير المطلوبين

### 2. إدارة المديونية

#### سيناريو إضافة المديونية:
1. **اختيار الشخص**: 
   - اختيار العميل أو الموظف من القائمة المسجلة مسبقاً
   - واجهة بحث وفلترة للعثور على الشخص المطلوب

2. **تفاصيل المديونية**:
   - **المبلغ**: إدخال قيمة المديونية (رقمية)
   - **التاريخ**: 
     - افتراضياً: الوقت الحالي
     - قابل للتعديل: إمكانية اختيار تاريخ مختلف
   - **الملاحظة**: حقل نصي اختياري لإضافة تفاصيل إضافية

3. **حفظ المديونية**: تسجيل المديونية في قاعدة البيانات

### 3. تصدير التقارير
- **تصدير مديونية فردية**: إنشاء ملف PDF لمديونية عميل أو موظف محدد
- **تصدير تقارير شاملة**: تقارير جماعية للمديونيات
- **تخصيص التقارير**: إمكانية اختيار فترة زمنية محددة
- **تنسيق PDF احترافي**: تصميم مناسب للطباعة والمشاركة

---

## البنية التقنية

### المنصة
- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **الإصدار المطلوب**: SDK ^3.5.4

### قاعدة البيانات المقترحة
- **SQLite**: قاعدة بيانات محلية للتطبيق
- **Hive**: بديل خفيف لتخزين البيانات
- **Firebase**: للمزامنة السحابية (اختياري)

### المكتبات المطلوبة
```yaml
dependencies:
  # واجهة المستخدم
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # واجهة حديثة وتفاعلية
  material_color_utilities: ^0.8.0
  animations: ^2.0.11
  flutter_staggered_animations: ^1.1.1

  # قاعدة البيانات
  sqflite: ^2.3.0
  path: ^1.8.3

  # إدارة الحالة
  provider: ^6.1.1

  # البحث والفرز
  flutter_typeahead: ^5.2.0
  searchable_listview: ^2.12.0

  # التاريخ والوقت
  intl: ^0.19.0

  # تصدير PDF
  pdf: ^3.10.7
  printing: ^5.12.0

  # مشاركة الملفات
  share_plus: ^7.2.1
  path_provider: ^2.1.1

  # أدوات إضافية للواجهة
  flutter_slidable: ^3.0.1
  pull_to_refresh: ^2.0.0
```

---

## هيكل قاعدة البيانات

### جدول العملاء (Customers)
```sql
CREATE TABLE customers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  address TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### جدول الموظفين (Employees)
```sql
CREATE TABLE employees (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  position TEXT,
  department TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### جدول المديونيات (Debts)
```sql
CREATE TABLE debts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  person_id INTEGER NOT NULL,
  person_type TEXT NOT NULL, -- 'customer' or 'employee'
  amount REAL NOT NULL,
  date DATETIME NOT NULL,
  notes TEXT,
  status TEXT DEFAULT 'active', -- 'active', 'paid', 'cancelled'
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

---

## متطلبات واجهة المستخدم الحديثة

### التصميم والمظهر
- **Material Design 3**: استخدام أحدث معايير التصميم
- **الألوان**: نظام ألوان متناسق ومريح للعين
- **الخطوط**: خطوط عربية واضحة ومقروءة
- **الرسوم المتحركة**: انتقالات سلسة وتفاعلية
- **الأيقونات**: أيقونات واضحة ومعبرة

### البحث والفرز المتقدم
- **البحث الفوري**: نتائج فورية أثناء الكتابة
- **البحث الذكي**: البحث في جميع الحقول
- **الفرز المتعدد**: فرز حسب معايير متعددة
- **الفلاتر**: تصفية البيانات حسب الفئات والتواريخ
- **حفظ البحث**: إمكانية حفظ عمليات البحث المتكررة

### التفاعل والاستجابة
- **اللمس المتعدد**: دعم الإيماءات الحديثة
- **التحديث بالسحب**: Pull-to-refresh للقوائم
- **التحميل التدريجي**: Lazy loading للبيانات الكبيرة
- **التنبيهات التفاعلية**: إشعارات جذابة ومفيدة
- **التأكيدات المرئية**: رسائل تأكيد واضحة للعمليات

---

## واجهات المستخدم المطلوبة

### 1. الشاشة الرئيسية
- عرض إحصائيات سريعة
- أزرار للوصول السريع للوظائف الرئيسية
- قائمة بآخر المديونيات المضافة

### 2. شاشة إدارة العملاء
- قائمة العملاء مع إمكانية البحث المتقدم
- فرز حسب الاسم، تاريخ الإضافة، أو المديونية
- واجهة حديثة مع بطاقات تفاعلية
- زر إضافة عميل جديد مع تصميم جذاب
- خيارات التعديل والحذف بتأكيد مرئي

### 3. شاشة إدارة الموظفين
- قائمة الموظفين مع إمكانية البحث المتقدم
- فرز حسب الاسم، القسم، أو المنصب
- واجهة حديثة مع بطاقات تفاعلية
- زر إضافة موظف جديد مع تصميم جذاب
- خيارات التعديل والحذف بتأكيد مرئي

### 4. شاشة إضافة مديونية
- اختيار نوع الشخص (عميل/موظف)
- قائمة منسدلة لاختيار الشخص
- حقول إدخال المبلغ والتاريخ والملاحظة
- زر حفظ المديونية

### 5. شاشة عرض المديونيات
- قائمة جميع المديونيات مع تصميم بطاقات حديث
- بحث متقدم حسب الاسم، المبلغ، أو الملاحظات
- فرز متعدد المعايير (التاريخ، المبلغ، النوع، الحالة)
- فلاتر تفاعلية مع عدادات للنتائج
- عرض إحصائيات سريعة (المجموع، العدد)
- خيارات التصدير والطباعة مع معاينة

### 6. شاشة التقارير
- اختيار نوع التقرير
- تحديد الفترة الزمنية
- معاينة التقرير قبل التصدير
- تصدير كـ PDF

---

## خطة التطوير

### المرحلة الأولى: الأساسيات
1. إعداد هيكل المشروع
2. تصميم قاعدة البيانات
3. إنشاء نماذج البيانات (Models)
4. تطوير واجهات إدارة العملاء والموظفين

### المرحلة الثانية: إدارة المديونية
1. تطوير واجهة إضافة المديونية
2. تطوير واجهة عرض المديونيات
3. إضافة وظائف البحث والفلترة
4. تطبيق إدارة الحالة

### المرحلة الثالثة: التقارير والتصدير
1. تطوير مولد تقارير PDF
2. تصميم قوالب التقارير
3. إضافة وظائف المشاركة والحفظ
4. اختبار التصدير على منصات مختلفة

### المرحلة الرابعة: التحسينات
1. تحسين واجهة المستخدم
2. إضافة الرسوم البيانية والإحصائيات
3. تطبيق النسخ الاحتياطي
4. اختبار شامل وإصلاح الأخطاء

---

## ملاحظات تقنية مهمة

### الأمان
- تشفير البيانات الحساسة
- التحقق من صحة المدخلات
- حماية من SQL Injection

### الأداء
- فهرسة قاعدة البيانات للبحث السريع
- تحسين استعلامات قاعدة البيانات
- إدارة الذاكرة بكفاءة

### سهولة الاستخدام
- واجهة مستخدم بديهية
- واجهة حديثة وممتعة للمستخدم
- البحث والفرز المتقدم
- دعم اللغة العربية
- رسائل خطأ واضحة ومفيدة

---

## معايير النجاح

1. **الوظيفية**: جميع المتطلبات الأساسية تعمل بشكل صحيح
2. **الأداء**: استجابة سريعة للواجهات والعمليات
3. **الموثوقية**: عدم فقدان البيانات وثبات التطبيق
4. **سهولة الاستخدام**: واجهة بديهية ومناسبة للمستخدمين
5. **جودة التقارير**: ملفات PDF احترافية وقابلة للطباعة

---

*تم إنشاء هذا المرجع في: 2025-06-27*
*آخر تحديث: 2025-06-27*
