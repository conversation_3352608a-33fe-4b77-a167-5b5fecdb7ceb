import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';

/// ويدجت البحث المتقدم
class AdvancedSearchWidget extends StatefulWidget {
  final String? initialQuery;
  final Function(String) onSearchChanged;
  final Function()? onClearSearch;
  final String hintText;
  final bool enabled;

  const AdvancedSearchWidget({
    super.key,
    this.initialQuery,
    required this.onSearchChanged,
    this.onClearSearch,
    this.hintText = AppConstants.search,
    this.enabled = true,
  });

  @override
  State<AdvancedSearchWidget> createState() => _AdvancedSearchWidgetState();
}

class _AdvancedSearchWidgetState extends State<AdvancedSearchWidget> {
  late TextEditingController _searchController;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);
    _isSearching = widget.initialQuery?.isNotEmpty ?? false;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        enabled: widget.enabled,
        onChanged: (value) {
          setState(() {
            _isSearching = value.isNotEmpty;
          });
          widget.onSearchChanged(value);
        },
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: Colors.grey[400],
            fontSize: 16,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: _isSearching ? AppTheme.primaryColor : Colors.grey[400],
          ),
          suffixIcon: _isSearching
              ? IconButton(
                  icon: const Icon(
                    Icons.clear,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _isSearching = false;
                    });
                    widget.onSearchChanged('');
                    if (widget.onClearSearch != null) {
                      widget.onClearSearch!();
                    }
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[50],
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: const TextStyle(
          fontSize: 16,
          color: AppTheme.textPrimaryColor,
        ),
      ),
    );
  }
}

/// ويدجت خيارات الفرز
class SortOptionsWidget extends StatelessWidget {
  final String currentSortBy;
  final bool ascending;
  final List<SortOption> sortOptions;
  final Function(String, bool) onSortChanged;

  const SortOptionsWidget({
    super.key,
    required this.currentSortBy,
    required this.ascending,
    required this.sortOptions,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Icon(
            Icons.sort,
            color: AppTheme.textSecondaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            '${AppConstants.sort}:',
            style: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: sortOptions.map((option) {
                  final isSelected = currentSortBy == option.value;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(option.label),
                          if (isSelected) ...[
                            const SizedBox(width: 4),
                            Icon(
                              ascending ? Icons.arrow_upward : Icons.arrow_downward,
                              size: 16,
                              color: Colors.white,
                            ),
                          ],
                        ],
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          if (isSelected) {
                            // إذا كان مختار، غير الاتجاه
                            onSortChanged(option.value, !ascending);
                          } else {
                            // إذا لم يكن مختار، اختره بالاتجاه الافتراضي
                            onSortChanged(option.value, option.defaultAscending);
                          }
                        }
                      },
                      selectedColor: AppTheme.primaryColor,
                      checkmarkColor: Colors.white,
                      labelStyle: TextStyle(
                        color: isSelected ? Colors.white : AppTheme.textPrimaryColor,
                        fontSize: 12,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج خيار الفرز
class SortOption {
  final String value;
  final String label;
  final bool defaultAscending;

  const SortOption({
    required this.value,
    required this.label,
    this.defaultAscending = true,
  });
}

/// ويدجت عرض نتائج البحث
class SearchResultsWidget extends StatelessWidget {
  final int totalResults;
  final String? searchQuery;
  final bool isLoading;

  const SearchResultsWidget({
    super.key,
    required this.totalResults,
    this.searchQuery,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              'جاري البحث...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppTheme.textSecondaryColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _buildResultText(),
              style: const TextStyle(
                color: AppTheme.textSecondaryColor,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _buildResultText() {
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      return 'تم العثور على $totalResults نتيجة للبحث عن "$searchQuery"';
    } else {
      return 'إجمالي النتائج: $totalResults';
    }
  }
}

/// ويدجت حالة فارغة للبحث
class EmptySearchWidget extends StatelessWidget {
  final String? searchQuery;
  final VoidCallback? onClearSearch;

  const EmptySearchWidget({
    super.key,
    this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              searchQuery != null && searchQuery!.isNotEmpty
                  ? Icons.search_off
                  : Icons.people_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              searchQuery != null && searchQuery!.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : 'لا توجد بيانات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              searchQuery != null && searchQuery!.isNotEmpty
                  ? 'جرب البحث بكلمات مختلفة'
                  : 'ابدأ بإضافة البيانات',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            if (searchQuery != null && searchQuery!.isNotEmpty && onClearSearch != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onClearSearch,
                icon: const Icon(Icons.clear),
                label: const Text('مسح البحث'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
