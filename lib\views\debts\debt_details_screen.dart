import 'package:flutter/material.dart';
import '../../models/debt.dart';
import '../../services/database/debts_service.dart';
import '../../services/database/customers_service.dart';
import '../../services/database/employees_service.dart';
import '../../constants/app_theme.dart';

/// شاشة تفاصيل المديونية
class DebtDetailsScreen extends StatefulWidget {
  final Debt debt;

  const DebtDetailsScreen({
    super.key,
    required this.debt,
  });

  @override
  State<DebtDetailsScreen> createState() => _DebtDetailsScreenState();
}

class _DebtDetailsScreenState extends State<DebtDetailsScreen> {
  final DebtsService _debtsService = DebtsService();
  final CustomersService _customersService = CustomersService();
  final EmployeesService _employeesService = EmployeesService();

  late Debt _debt;
  String? _personName;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _debt = widget.debt;
    _loadPersonName();
  }

  /// تحميل اسم الشخص
  Future<void> _loadPersonName() async {
    try {
      if (_debt.isCustomerDebt) {
        final customer =
            await _customersService.getCustomerById(_debt.personId);
        _personName = customer?.name ?? 'عميل محذوف';
      } else if (_debt.isEmployeeDebt) {
        final employee =
            await _employeesService.getEmployeeById(_debt.personId);
        _personName = employee?.name ?? 'موظف محذوف';
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      _personName = 'غير محدد';
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('تفاصيل المديونية'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // زر القائمة
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'change_status',
              child: Row(
                children: [
                  Icon(
                    _debt.isActive ? Icons.check_circle : Icons.pending,
                    color: _debt.isActive ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Text(_debt.isActive ? 'تحديد كمدفوعة' : 'إعادة تفعيل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة المعلومات الأساسية
          _buildBasicInfoCard(),

          const SizedBox(height: 16),

          // بطاقة تفاصيل الشخص
          _buildPersonCard(),

          const SizedBox(height: 16),

          // بطاقة الملاحظات
          if (_debt.notes != null && _debt.notes!.isNotEmpty) _buildNotesCard(),

          // بطاقة الصورة
          if (_debt.imagePath != null && _debt.imagePath!.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildImageCard(),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة المعلومات الأساسية
  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // المبلغ والحالة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _debt.formattedAmount,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: _getStatusColor()),
                  ),
                  child: Text(
                    _debt.statusText,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // التاريخ
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 20,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'تاريخ المديونية',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              _debt.formattedDate,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 16),

            // نوع الشخص
            Row(
              children: [
                Icon(
                  _debt.isCustomerDebt ? Icons.person : Icons.badge,
                  size: 20,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'نوع الشخص',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              _debt.isCustomerDebt ? 'عميل' : 'موظف',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الشخص
  Widget _buildPersonCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _debt.isCustomerDebt ? Icons.person : Icons.badge,
                  color: _debt.isCustomerDebt
                      ? AppTheme.secondaryColor
                      : Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  _debt.isCustomerDebt ? 'العميل' : 'الموظف',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _debt.isCustomerDebt
                        ? AppTheme.secondaryColor
                        : Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _personName ?? 'جاري التحميل...',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الصورة
  Widget _buildImageCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.image,
                  color: Colors.green[700],
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الصورة المرفقة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            GestureDetector(
              onTap: () => _viewImage(_debt.imagePath!),
              child: Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    _debt.imagePath!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[100],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.broken_image,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'لا يمكن عرض الصورة',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط لعرض الصورة بالحجم الكامل',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الشريط السفلي
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر تغيير الحالة
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _changeStatus,
              icon: Icon(_debt.isActive ? Icons.check_circle : Icons.pending),
              label: Text(_debt.isActive ? 'تحديد كمدفوعة' : 'إعادة تفعيل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _debt.isActive ? Colors.green : Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // زر التعديل
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _editDebt,
              icon: const Icon(Icons.edit),
              label: const Text('تعديل'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: const BorderSide(color: AppTheme.primaryColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor() {
    switch (_debt.status) {
      case 'active':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// عرض الصورة
  void _viewImage(String imagePath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: const Text('عرض الصورة'),
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              Expanded(
                child: Image.asset(
                  imagePath,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[100],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا يمكن عرض الصورة',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _editDebt();
        break;
      case 'change_status':
        _changeStatus();
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
    }
  }

  /// تعديل المديونية
  void _editDebt() {
    // سيتم تطوير شاشة التعديل لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة التعديل ستتوفر قريباً'),
      ),
    );
  }

  /// تغيير حالة المديونية
  Future<void> _changeStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final newStatus = _debt.isActive ? 'paid' : 'active';
      final updatedDebt = _debt.copyWith(status: newStatus);

      await _debtsService.updateDebt(updatedDebt);

      setState(() {
        _debt = updatedDebt;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _debt.isActive
                  ? 'تم إعادة تفعيل المديونية'
                  : 'تم تحديد المديونية كمدفوعة',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الحالة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف هذه المديونية؟\n\n'
            'المبلغ: ${_debt.formattedAmount}\n'
            'التاريخ: ${_debt.formattedDate}\n\n'
            'سيتم حذف المديونية نهائياً.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteDebt();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// حذف المديونية
  Future<void> _deleteDebt() async {
    try {
      await _debtsService.deleteDebt(_debt.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم حذف المديونية بمبلغ ${_debt.formattedAmount} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.of(context).pop(true); // العودة مع إشارة التحديث
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المديونية: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// بناء بطاقة الملاحظات
  Widget _buildNotesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note,
                  color: Colors.purple[700],
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الملاحظات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _debt.notes!,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
