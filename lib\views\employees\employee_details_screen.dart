import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/employee.dart';
import '../../models/debt.dart';
import '../../services/database/debts_service.dart';
import '../../constants/app_theme.dart';
import 'edit_employee_screen.dart';

/// شاشة تفاصيل الموظف مع إحصائيات المديونيات
class EmployeeDetailsScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailsScreen({
    super.key,
    required this.employee,
  });

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  final DebtsService _debtsService = DebtsService();

  List<Debt> _employeeDebts = [];
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // إحصائيات المديونيات
  double _totalActiveDebt = 0.0;
  double _totalPaidDebt = 0.0;
  double _totalDebt = 0.0;
  int _activeDebtsCount = 0;
  int _paidDebtsCount = 0;
  int _totalDebtsCount = 0;

  @override
  void initState() {
    super.initState();
    _loadEmployeeDebts();
  }

  /// تحميل مديونيات الموظف
  Future<void> _loadEmployeeDebts() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final debts = await _debtsService.getDebtsByPersonId(
        widget.employee.id!,
        'employee',
      );

      _calculateStatistics(debts);

      setState(() {
        _employeeDebts = debts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'خطأ في تحميل المديونيات: ${e.toString()}';
      });
    }
  }

  /// حساب إحصائيات المديونيات
  void _calculateStatistics(List<Debt> debts) {
    _totalActiveDebt = 0.0;
    _totalPaidDebt = 0.0;
    _totalDebt = 0.0;
    _activeDebtsCount = 0;
    _paidDebtsCount = 0;
    _totalDebtsCount = debts.length;

    for (final debt in debts) {
      _totalDebt += debt.amount;

      if (debt.status == 'active') {
        _totalActiveDebt += debt.amount;
        _activeDebtsCount++;
      } else if (debt.status == 'paid') {
        _totalPaidDebt += debt.amount;
        _paidDebtsCount++;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.employee.name),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // زر التعديل
        IconButton(
          onPressed: _navigateToEditEmployee,
          icon: const Icon(Icons.edit),
          tooltip: 'تعديل بيانات الموظف',
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة معلومات الموظف
          _buildEmployeeInfoCard(),

          const SizedBox(height: 16),

          // بطاقة إحصائيات المديونيات
          _buildDebtStatisticsCard(),

          const SizedBox(height: 16),

          // قائمة المديونيات
          _buildDebtsSection(),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات الموظف
  Widget _buildEmployeeInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withOpacity(0.8)
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            // الحروف الأولى
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Center(
                child: Text(
                  widget.employee.initials,
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // اسم الموظف
            Text(
              widget.employee.name,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            // المنصب والقسم
            if (widget.employee.hasWorkInfo) ...[
              const SizedBox(height: 8),
              if (widget.employee.position != null)
                Text(
                  widget.employee.position!,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              if (widget.employee.department != null)
                Text(
                  widget.employee.department!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white60,
                  ),
                  textAlign: TextAlign.center,
                ),
            ],

            // معلومات الاتصال
            if (widget.employee.hasContactInfo) ...[
              const SizedBox(height: 16),
              _buildContactInfo(),
            ],

            // تاريخ الإضافة
            const SizedBox(height: 12),
            Text(
              'تاريخ الإضافة: ${widget.employee.formattedCreatedAt}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white60,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الاتصال
  Widget _buildContactInfo() {
    return Column(
      children: [
        if (widget.employee.phone != null && widget.employee.phone!.isNotEmpty)
          _buildContactRow(
            icon: Icons.phone,
            text: widget.employee.phone!,
          ),
        if (widget.employee.email != null &&
            widget.employee.email!.isNotEmpty) ...[
          if (widget.employee.phone != null &&
              widget.employee.phone!.isNotEmpty)
            const SizedBox(height: 8),
          _buildContactRow(
            icon: Icons.email,
            text: widget.employee.email!,
          ),
        ],
      ],
    );
  }

  /// بناء صف معلومات الاتصال
  Widget _buildContactRow({
    required IconData icon,
    required String text,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.white70,
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائيات المديونيات
  Widget _buildDebtStatisticsCard() {
    if (_isLoading) {
      return const Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (_hasError) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                'خطأ في تحميل الإحصائيات',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppTheme.secondaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات المديونيات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    title: 'المديونيات النشطة',
                    value: '${_totalActiveDebt.toStringAsFixed(2)} ر.س',
                    count: '$_activeDebtsCount مديونية',
                    color: Colors.orange,
                    icon: Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    title: 'المديونيات المدفوعة',
                    value: '${_totalPaidDebt.toStringAsFixed(2)} ر.س',
                    count: '$_paidDebtsCount مديونية',
                    color: Colors.green,
                    icon: Icons.check_circle,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الإجمالي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.primaryColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    'إجمالي المديونيات',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.primaryColor.withOpacity(0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_totalDebt.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  Text(
                    '$_totalDebtsCount مديونية',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem({
    required String title,
    required String value,
    required String count,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            count,
            style: TextStyle(
              fontSize: 10,
              color: color.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم المديونيات
  Widget _buildDebtsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'المديونيات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            if (_totalDebtsCount > 0)
              Text(
                '$_totalDebtsCount مديونية',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
          ],
        ),

        const SizedBox(height: 16),

        // قائمة المديونيات
        _buildDebtsList(),
      ],
    );
  }

  /// بناء قائمة المديونيات
  Widget _buildDebtsList() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                'خطأ في تحميل المديونيات',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red[700],
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _loadEmployeeDebts,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_employeeDebts.isEmpty) {
      return Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد مديونيات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'لم يتم تسجيل أي مديونيات لهذا الموظف بعد',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _employeeDebts.length,
        itemBuilder: (context, index) {
          final debt = _employeeDebts[index];

          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildDebtCard(debt),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء بطاقة المديونية
  Widget _buildDebtCard(Debt debt) {
    final isActive = debt.status == 'active';
    final statusColor = isActive ? Colors.orange : Colors.green;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الصف الأول: المبلغ والحالة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // المبلغ
                Text(
                  '${debt.amount.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),

                // حالة المديونية
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: statusColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isActive ? Icons.pending : Icons.check_circle,
                        size: 16,
                        color: statusColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        isActive ? 'نشطة' : 'مدفوعة',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // التاريخ
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  debt.formattedDate,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),

            // الملاحظات
            if (debt.notes != null && debt.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.note,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      debt.notes!,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء زر الإجراءات العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _navigateToEditEmployee,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      child: const Icon(Icons.edit),
    );
  }

  /// التنقل لتعديل الموظف
  void _navigateToEditEmployee() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditEmployeeScreen(employee: widget.employee),
      ),
    );

    if (result == true) {
      // إعادة تحميل البيانات إذا تم التعديل
      Navigator.of(context).pop(true);
    }
  }
}
