import '../../constants/app_constants.dart';
import '../../models/customer.dart';
import 'database_helper.dart';

/// خدمة إدارة العملاء في قاعدة البيانات
class CustomersService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة عميل جديد
  Future<int> addCustomer(Customer customer) async {
    final customerMap = customer.toMap();
    customerMap.remove('id'); // إزالة المعرف لأنه سيتم إنشاؤه تلقائياً
    return await _dbHelper.insert(AppConstants.customersTable, customerMap);
  }

  /// تحديث عميل موجود
  Future<int> updateCustomer(Customer customer) async {
    if (customer.id == null) {
      throw ArgumentError('Customer ID cannot be null for update operation');
    }
    
    final customerMap = customer.toMap();
    return await _dbHelper.update(
      AppConstants.customersTable,
      customerMap,
      'id = ?',
      [customer.id],
    );
  }

  /// حذف عميل
  Future<int> deleteCustomer(int customerId) async {
    return await _dbHelper.delete(
      AppConstants.customersTable,
      'id = ?',
      [customerId],
    );
  }

  /// الحصول على عميل بالمعرف
  Future<Customer?> getCustomerById(int customerId) async {
    final results = await _dbHelper.query(
      AppConstants.customersTable,
      where: 'id = ?',
      whereArgs: [customerId],
    );

    if (results.isNotEmpty) {
      return Customer.fromMap(results.first);
    }
    return null;
  }

  /// الحصول على جميع العملاء
  Future<List<Customer>> getAllCustomers({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final results = await _dbHelper.query(
      AppConstants.customersTable,
      orderBy: orderBy ?? 'name ASC',
      limit: limit,
      offset: offset,
    );

    return results.map((map) => Customer.fromMap(map)).toList();
  }

  /// البحث في العملاء
  Future<List<Customer>> searchCustomers(
    String searchTerm, {
    String? orderBy,
    int? limit,
  }) async {
    final results = await _dbHelper.search(
      AppConstants.customersTable,
      searchTerm,
      searchColumns: ['name', 'phone', 'email', 'address'],
      orderBy: orderBy ?? 'name ASC',
      limit: limit,
    );

    return results.map((map) => Customer.fromMap(map)).toList();
  }

  /// الحصول على عدد العملاء
  Future<int> getCustomersCount() async {
    final results = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.customersTable}',
    );
    return results.first['count'] as int;
  }

  /// الحصول على العملاء مع إجمالي مديونياتهم
  Future<List<Map<String, dynamic>>> getCustomersWithDebts({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final sql = '''
      SELECT 
        c.*,
        COALESCE(SUM(CASE WHEN d.status = '${AppConstants.debtStatusActive}' THEN d.amount ELSE 0 END), 0) as total_active_debt,
        COALESCE(SUM(d.amount), 0) as total_debt,
        COUNT(d.id) as debt_count
      FROM ${AppConstants.customersTable} c
      LEFT JOIN ${AppConstants.debtsTable} d ON c.id = d.person_id AND d.person_type = '${AppConstants.personTypeCustomer}'
      GROUP BY c.id
      ORDER BY ${orderBy ?? 'c.name ASC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    return await _dbHelper.rawQuery(sql);
  }

  /// الحصول على العملاء الذين لديهم مديونيات نشطة
  Future<List<Customer>> getCustomersWithActiveDebts({
    String? orderBy,
    int? limit,
  }) async {
    final sql = '''
      SELECT DISTINCT c.*
      FROM ${AppConstants.customersTable} c
      INNER JOIN ${AppConstants.debtsTable} d ON c.id = d.person_id 
      WHERE d.person_type = '${AppConstants.personTypeCustomer}' 
        AND d.status = '${AppConstants.debtStatusActive}'
      ORDER BY ${orderBy ?? 'c.name ASC'}
      ${limit != null ? 'LIMIT $limit' : ''}
    ''';

    final results = await _dbHelper.rawQuery(sql);
    return results.map((map) => Customer.fromMap(map)).toList();
  }

  /// فرز العملاء حسب معايير مختلفة
  Future<List<Customer>> getSortedCustomers({
    required String sortBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    String orderBy;
    
    switch (sortBy.toLowerCase()) {
      case 'name':
        orderBy = 'name ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'phone':
        orderBy = 'phone ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'email':
        orderBy = 'email ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'created_at':
        orderBy = 'created_at ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'updated_at':
        orderBy = 'updated_at ${ascending ? 'ASC' : 'DESC'}';
        break;
      default:
        orderBy = 'name ASC';
    }

    return await getAllCustomers(
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  /// التحقق من وجود عميل بنفس الاسم
  Future<bool> isCustomerNameExists(String name, {int? excludeId}) async {
    String whereClause = 'LOWER(name) = LOWER(?)';
    List<dynamic> whereArgs = [name];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final results = await _dbHelper.query(
      AppConstants.customersTable,
      where: whereClause,
      whereArgs: whereArgs,
      limit: 1,
    );

    return results.isNotEmpty;
  }

  /// التحقق من وجود عميل برقم هاتف معين
  Future<bool> isCustomerPhoneExists(String phone, {int? excludeId}) async {
    if (phone.isEmpty) return false;

    String whereClause = 'phone = ?';
    List<dynamic> whereArgs = [phone];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final results = await _dbHelper.query(
      AppConstants.customersTable,
      where: whereClause,
      whereArgs: whereArgs,
      limit: 1,
    );

    return results.isNotEmpty;
  }

  /// التحقق من وجود عميل ببريد إلكتروني معين
  Future<bool> isCustomerEmailExists(String email, {int? excludeId}) async {
    if (email.isEmpty) return false;

    String whereClause = 'LOWER(email) = LOWER(?)';
    List<dynamic> whereArgs = [email];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final results = await _dbHelper.query(
      AppConstants.customersTable,
      where: whereClause,
      whereArgs: whereArgs,
      limit: 1,
    );

    return results.isNotEmpty;
  }
}
