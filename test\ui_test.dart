import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:faqeh/main.dart';
import 'package:faqeh/constants/app_constants.dart';

void main() {
  group('اختبارات واجهة المستخدم', () {
    testWidgets('اختبار الشاشة الرئيسية', (WidgetTester tester) async {
      // بناء التطبيق
      await tester.pumpWidget(const FaqehApp());

      // التحقق من وجود اسم التطبيق
      expect(find.text(AppConstants.appName), findsOneWidget);

      // التحقق من وجود الأزرار الرئيسية
      expect(find.text(AppConstants.customers), findsOneWidget);
      expect(find.text(AppConstants.employees), findsOneWidget);
      expect(find.text('المديونيات'), findsOneWidget);
      expect(find.text('التقارير'), findsOneWidget);

      // التحقق من وجود الإحصائيات
      expect(find.text('الإحصائيات السريعة'), findsOneWidget);
      expect(find.text('إجمالي العملاء'), findsOneWidget);
      expect(find.text('إجمالي الموظفين'), findsOneWidget);

      // التحقق من وجود قسم آخر المديونيات
      expect(find.text('آخر المديونيات المضافة'), findsOneWidget);
    });

    testWidgets('اختبار التنقل إلى شاشة العملاء', (WidgetTester tester) async {
      await tester.pumpWidget(const FaqehApp());

      // البحث عن زر العملاء والنقر عليه
      final customersButton = find.ancestor(
        of: find.text('العملاء'),
        matching: find.byType(InkWell),
      );
      
      expect(customersButton, findsOneWidget);
      await tester.tap(customersButton);
      await tester.pumpAndSettle();

      // التحقق من الانتقال إلى شاشة العملاء
      expect(find.text(AppConstants.customers), findsOneWidget);
    });

    testWidgets('اختبار التنقل إلى شاشة الموظفين', (WidgetTester tester) async {
      await tester.pumpWidget(const FaqehApp());

      // البحث عن زر الموظفين والنقر عليه
      final employeesButton = find.ancestor(
        of: find.text('الموظفين'),
        matching: find.byType(InkWell),
      );
      
      expect(employeesButton, findsOneWidget);
      await tester.tap(employeesButton);
      await tester.pumpAndSettle();

      // التحقق من الانتقال إلى شاشة الموظفين
      expect(find.text('الموظفون'), findsOneWidget);
    });

    testWidgets('اختبار التنقل إلى شاشة المديونيات', (WidgetTester tester) async {
      await tester.pumpWidget(const FaqehApp());

      // البحث عن زر المديونيات والنقر عليه
      final debtsButton = find.ancestor(
        of: find.text('المديونيات'),
        matching: find.byType(InkWell),
      );
      
      expect(debtsButton, findsOneWidget);
      await tester.tap(debtsButton);
      await tester.pumpAndSettle();

      // التحقق من الانتقال إلى شاشة المديونيات
      expect(find.text('المديونيات'), findsOneWidget);
    });

    testWidgets('اختبار التنقل إلى شاشة التقارير', (WidgetTester tester) async {
      await tester.pumpWidget(const FaqehApp());

      // البحث عن زر التقارير والنقر عليه
      final reportsButton = find.ancestor(
        of: find.text('التقارير'),
        matching: find.byType(InkWell),
      );
      
      expect(reportsButton, findsOneWidget);
      await tester.tap(reportsButton);
      await tester.pumpAndSettle();

      // التحقق من الانتقال إلى شاشة التقارير
      expect(find.text('تقارير المديونيات'), findsOneWidget);
    });

    testWidgets('اختبار الثيم والألوان', (WidgetTester tester) async {
      await tester.pumpWidget(const FaqehApp());

      // التحقق من تطبيق الثيم
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.theme, isNotNull);
      expect(materialApp.title, equals(AppConstants.appName));
      expect(materialApp.debugShowCheckedModeBanner, isFalse);
    });

    testWidgets('اختبار استجابة الواجهة', (WidgetTester tester) async {
      // اختبار بأحجام شاشة مختلفة
      await tester.binding.setSurfaceSize(const Size(400, 800)); // هاتف عادي
      await tester.pumpWidget(const FaqehApp());
      
      // التحقق من وجود العناصر الأساسية
      expect(find.text(AppConstants.appName), findsOneWidget);
      expect(find.text('العملاء'), findsOneWidget);

      // اختبار بحجم شاشة أكبر
      await tester.binding.setSurfaceSize(const Size(800, 1200)); // تابلت
      await tester.pumpWidget(const FaqehApp());
      
      // التحقق من استمرار عمل الواجهة
      expect(find.text(AppConstants.appName), findsOneWidget);
      expect(find.text('العملاء'), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار إمكانية الوصول', (WidgetTester tester) async {
      await tester.pumpWidget(const FaqehApp());

      // التحقق من وجود semantics للعناصر المهمة
      expect(find.byType(Semantics), findsWidgets);
      
      // التحقق من إمكانية التنقل بالكيبورد
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pump();
    });

    group('اختبارات التفاعل', () {
      testWidgets('اختبار النقر على البطاقات', (WidgetTester tester) async {
        await tester.pumpWidget(const FaqehApp());

        // البحث عن بطاقات الوصول السريع
        final cards = find.byType(Card);
        expect(cards, findsWidgets);

        // اختبار النقر على أول بطاقة
        if (cards.evaluate().isNotEmpty) {
          await tester.tap(cards.first);
          await tester.pumpAndSettle();
        }
      });

      testWidgets('اختبار التمرير', (WidgetTester tester) async {
        await tester.pumpWidget(const FaqehApp());

        // البحث عن SingleChildScrollView
        final scrollView = find.byType(SingleChildScrollView);
        expect(scrollView, findsOneWidget);

        // اختبار التمرير
        await tester.drag(scrollView, const Offset(0, -200));
        await tester.pumpAndSettle();
      });
    });

    group('اختبارات الحالات الحدية', () {
      testWidgets('اختبار عدم وجود بيانات', (WidgetTester tester) async {
        await tester.pumpWidget(const FaqehApp());

        // التحقق من رسالة عدم وجود مديونيات
        expect(find.text('لا توجد مديونيات مضافة بعد'), findsOneWidget);
        expect(find.text('ستظهر آخر المديونيات المضافة هنا'), findsOneWidget);
      });

      testWidgets('اختبار الأيقونات والرموز', (WidgetTester tester) async {
        await tester.pumpWidget(const FaqehApp());

        // التحقق من وجود الأيقونات المطلوبة
        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
        expect(find.byIcon(Icons.people), findsWidgets);
        expect(find.byIcon(Icons.badge), findsWidgets);
        expect(find.byIcon(Icons.receipt_long), findsWidgets);
        expect(find.byIcon(Icons.assessment), findsWidgets);
      });
    });
  });
}
