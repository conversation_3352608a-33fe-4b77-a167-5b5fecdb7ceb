import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../models/debt.dart';
import '../../services/database/debts_service.dart';
import '../../services/database/customers_service.dart';
import '../../services/database/employees_service.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/advanced_search_widget.dart';
import '../../widgets/debts/debt_card.dart';
import 'add_debt_screen.dart';
import 'debt_details_screen.dart';

/// شاشة قائمة المديونيات مع البحث والفرز المتقدم
class DebtsListScreen extends StatefulWidget {
  const DebtsListScreen({super.key});

  @override
  State<DebtsListScreen> createState() => _DebtsListScreenState();
}

class _DebtsListScreenState extends State<DebtsListScreen> {
  final DebtsService _debtsService = DebtsService();
  final CustomersService _customersService = CustomersService();
  final EmployeesService _employeesService = EmployeesService();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<Map<String, dynamic>> _allDebtsWithPersons = [];
  List<Map<String, dynamic>> _filteredDebts = [];

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // متغيرات البحث والفرز
  String _searchQuery = '';
  String _currentSortBy = 'date';
  bool _isAscending = false;
  String _statusFilter = 'all'; // all, active, paid, cancelled
  String _personTypeFilter = 'all'; // all, customer, employee

  // إحصائيات
  int get _totalDebts => _allDebtsWithPersons.length;
  int get _filteredCount => _filteredDebts.length;
  double get _activeAmount => _filteredDebts
      .where((item) => (item['debt'] as Debt).isActive)
      .fold(0.0, (sum, item) => sum + (item['debt'] as Debt).amount);

  @override
  void initState() {
    super.initState();
    _loadDebts();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// تحميل قائمة المديونيات مع أسماء الأشخاص
  Future<void> _loadDebts() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // تحميل جميع المديونيات
      final debts = await _debtsService.getAllDebts();

      // تحميل أسماء الأشخاص
      final debtsWithPersons = <Map<String, dynamic>>[];

      for (final debt in debts) {
        String personName = 'غير محدد';

        try {
          if (debt.isCustomerDebt) {
            final customer =
                await _customersService.getCustomerById(debt.personId);
            personName = customer?.name ?? 'عميل محذوف';
          } else if (debt.isEmployeeDebt) {
            final employee =
                await _employeesService.getEmployeeById(debt.personId);
            personName = employee?.name ?? 'موظف محذوف';
          }
        } catch (e) {
          // في حالة عدم وجود الشخص
          personName = debt.isCustomerDebt ? 'عميل محذوف' : 'موظف محذوف';
        }

        debtsWithPersons.add({
          'debt': debt,
          'personName': personName,
        });
      }

      // ترتيب البيانات
      _sortDebts(debtsWithPersons);

      setState(() {
        _allDebtsWithPersons = debtsWithPersons;
        _applyFilters();
        _isLoading = false;
      });

      _refreshController.refreshCompleted();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'خطأ في تحميل المديونيات: ${e.toString()}';
      });
      _refreshController.refreshFailed();
    }
  }

  /// ترتيب المديونيات
  void _sortDebts(List<Map<String, dynamic>> debts) {
    debts.sort((a, b) {
      final debtA = a['debt'] as Debt;
      final debtB = b['debt'] as Debt;

      int comparison = 0;

      switch (_currentSortBy) {
        case 'date':
          comparison = debtA.date.compareTo(debtB.date);
          break;
        case 'amount':
          comparison = debtA.amount.compareTo(debtB.amount);
          break;
        case 'person':
          comparison =
              (a['personName'] as String).compareTo(b['personName'] as String);
          break;
        case 'status':
          comparison = debtA.status.compareTo(debtB.status);
          break;
        case 'type':
          comparison = debtA.personType.compareTo(debtB.personType);
          break;
        default:
          comparison = debtA.date.compareTo(debtB.date);
      }

      return _isAscending ? comparison : -comparison;
    });
  }

  /// تطبيق الفلاتر والبحث
  void _applyFilters() {
    var filtered = List<Map<String, dynamic>>.from(_allDebtsWithPersons);

    // فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((item) {
        final debt = item['debt'] as Debt;
        final personName = item['personName'] as String;

        return personName.toLowerCase().contains(query) ||
            debt.amount.toString().contains(query) ||
            debt.formattedAmount.toLowerCase().contains(query) ||
            (debt.notes?.toLowerCase().contains(query) ?? false) ||
            debt.formattedDate.contains(query);
      }).toList();
    }

    // فلتر الحالة
    if (_statusFilter != 'all') {
      filtered = filtered.where((item) {
        final debt = item['debt'] as Debt;
        return debt.status == _statusFilter;
      }).toList();
    }

    // فلتر نوع الشخص
    if (_personTypeFilter != 'all') {
      filtered = filtered.where((item) {
        final debt = item['debt'] as Debt;
        return debt.personType == _personTypeFilter;
      }).toList();
    }

    setState(() {
      _filteredDebts = filtered;
    });
  }

  /// البحث في المديونيات
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  /// تغيير معايير الفرز
  void _onSortChanged(String sortBy, bool ascending) {
    setState(() {
      _currentSortBy = sortBy;
      _isAscending = ascending;
    });
    _sortDebts(_allDebtsWithPersons);
    _applyFilters();
  }

  /// تغيير فلتر الحالة
  void _onStatusFilterChanged(String status) {
    setState(() {
      _statusFilter = status;
    });
    _applyFilters();
  }

  /// تغيير فلتر نوع الشخص
  void _onPersonTypeFilterChanged(String type) {
    setState(() {
      _personTypeFilter = type;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('المديونيات'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // عرض الإحصائيات
        if (!_isLoading)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _searchQuery.isEmpty
                        ? '$_totalDebts مديونية'
                        : '$_filteredCount من $_totalDebts',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_filteredCount > 0)
                    Text(
                      '${_activeAmount.toStringAsFixed(0)} ر.س نشطة',
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return Column(
      children: [
        // ويدجت البحث والفلاتر
        _buildSearchAndFilters(),

        // المحتوى الرئيسي
        Expanded(
          child: _buildMainContent(),
        ),
      ],
    );
  }

  /// بناء ويدجت البحث والفلاتر
  Widget _buildSearchAndFilters() {
    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // حقل البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: AdvancedSearchWidget(
              hintText: 'البحث في المديونيات...',
              onSearchChanged: _onSearchChanged,
              initialQuery: _searchQuery,
            ),
          ),

          // الفلاتر والفرز
          if (_totalDebts > 0) ...[
            _buildFiltersRow(),
            _buildSortOptions(),
          ],
        ],
      ),
    );
  }

  /// بناء صف الفلاتر
  Widget _buildFiltersRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // فلتر الحالة
          Expanded(
            child: _buildFilterChip(
              label: 'الحالة',
              value: _getStatusFilterText(),
              onTap: _showStatusFilterDialog,
            ),
          ),

          const SizedBox(width: 12),

          // فلتر نوع الشخص
          Expanded(
            child: _buildFilterChip(
              label: 'النوع',
              value: _getPersonTypeFilterText(),
              onTap: _showPersonTypeFilterDialog,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريحة الفلتر
  Widget _buildFilterChip({
    required String label,
    required String value,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$label: ',
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء خيارات الفرز
  Widget _buildSortOptions() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: SortOptionsWidget(
        currentSortBy: _currentSortBy,
        ascending: _isAscending,
        onSortChanged: _onSortChanged,
        sortOptions: const [
          SortOption(value: 'date', label: 'التاريخ'),
          SortOption(value: 'amount', label: 'المبلغ'),
          SortOption(value: 'person', label: 'الشخص'),
          SortOption(value: 'status', label: 'الحالة'),
          SortOption(value: 'type', label: 'النوع'),
        ],
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_hasError) {
      return _buildErrorState();
    }

    if (_totalDebts == 0) {
      return _buildEmptyState();
    }

    if (_filteredDebts.isEmpty) {
      return _buildNoResultsState();
    }

    return _buildDebtsList();
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل المديونيات...',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadDebts,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة عدم وجود مديونيات
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد مديونيات',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ابدأ بإضافة مديونية جديدة لتتبع المبالغ المستحقة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _navigateToAddDebt,
              icon: const Icon(Icons.add),
              label: const Text('إضافة مديونية جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة عدم وجود نتائج بحث
  Widget _buildNoResultsState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على مديونيات تطابق معايير البحث والفلترة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _searchQuery = '';
                  _statusFilter = 'all';
                  _personTypeFilter = 'all';
                });
                _applyFilters();
              },
              icon: const Icon(Icons.clear),
              label: const Text('مسح الفلاتر'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المديونيات
  Widget _buildDebtsList() {
    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _loadDebts,
      header: const WaterDropMaterialHeader(
        backgroundColor: AppTheme.primaryColor,
        color: Colors.white,
      ),
      child: AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredDebts.length,
          itemBuilder: (context, index) {
            final item = _filteredDebts[index];
            final debt = item['debt'] as Debt;
            final personName = item['personName'] as String;

            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: DebtCard(
                      debt: debt,
                      personName: personName,
                      onTap: () => _navigateToDebtDetails(debt),
                      onEdit: () => _navigateToEditDebt(debt),
                      onDelete: () => _showDeleteConfirmation(debt),
                      onChangeStatus: () => _changeDebtStatus(debt),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToAddDebt,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('إضافة مديونية'),
    );
  }

  /// الحصول على نص فلتر الحالة
  String _getStatusFilterText() {
    switch (_statusFilter) {
      case 'active':
        return 'نشطة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return 'الكل';
    }
  }

  /// الحصول على نص فلتر نوع الشخص
  String _getPersonTypeFilterText() {
    switch (_personTypeFilter) {
      case 'customer':
        return 'عملاء';
      case 'employee':
        return 'موظفين';
      default:
        return 'الكل';
    }
  }

  /// عرض حوار فلتر الحالة
  void _showStatusFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة حسب الحالة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFilterOption('الكل', 'all', _statusFilter),
            _buildFilterOption('نشطة', 'active', _statusFilter),
            _buildFilterOption('مدفوعة', 'paid', _statusFilter),
            _buildFilterOption('ملغاة', 'cancelled', _statusFilter),
          ],
        ),
      ),
    );
  }

  /// عرض حوار فلتر نوع الشخص
  void _showPersonTypeFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة حسب النوع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFilterOption('الكل', 'all', _personTypeFilter),
            _buildFilterOption('عملاء', 'customer', _personTypeFilter),
            _buildFilterOption('موظفين', 'employee', _personTypeFilter),
          ],
        ),
      ),
    );
  }

  /// بناء خيار الفلتر
  Widget _buildFilterOption(String label, String value, String currentValue) {
    return ListTile(
      title: Text(label),
      leading: Radio<String>(
        value: value,
        groupValue: currentValue,
        onChanged: (newValue) {
          Navigator.of(context).pop();
          if (newValue != null) {
            if (currentValue == _statusFilter) {
              _onStatusFilterChanged(newValue);
            } else {
              _onPersonTypeFilterChanged(newValue);
            }
          }
        },
      ),
      onTap: () {
        Navigator.of(context).pop();
        if (currentValue == _statusFilter) {
          _onStatusFilterChanged(value);
        } else {
          _onPersonTypeFilterChanged(value);
        }
      },
    );
  }

  /// التنقل لإضافة مديونية جديدة
  void _navigateToAddDebt() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddDebtScreen(),
      ),
    );

    if (result == true) {
      _loadDebts();
    }
  }

  /// التنقل لتفاصيل المديونية
  void _navigateToDebtDetails(Debt debt) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DebtDetailsScreen(debt: debt),
      ),
    );

    if (result == true) {
      _loadDebts();
    }
  }

  /// التنقل لتعديل المديونية
  void _navigateToEditDebt(Debt debt) async {
    // سيتم تطوير شاشة التعديل لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة التعديل ستتوفر قريباً'),
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Debt debt) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف هذه المديونية؟\n\n'
            'المبلغ: ${debt.formattedAmount}\n'
            'التاريخ: ${debt.formattedDate}\n\n'
            'سيتم حذف المديونية نهائياً.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteDebt(debt);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// حذف مديونية
  void _deleteDebt(Debt debt) async {
    try {
      await _debtsService.deleteDebt(debt.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم حذف المديونية بمبلغ ${debt.formattedAmount} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        _loadDebts();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المديونية: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تغيير حالة المديونية
  void _changeDebtStatus(Debt debt) async {
    try {
      final newStatus = debt.isActive ? 'paid' : 'active';
      final updatedDebt = debt.copyWith(status: newStatus);

      await _debtsService.updateDebt(updatedDebt);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              debt.isActive
                  ? 'تم تحديد المديونية كمدفوعة'
                  : 'تم إعادة تفعيل المديونية',
            ),
            backgroundColor: Colors.green,
          ),
        );

        _loadDebts();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث حالة المديونية: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
