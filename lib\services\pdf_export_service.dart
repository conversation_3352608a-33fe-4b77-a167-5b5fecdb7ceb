import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import '../models/debt.dart';
import 'reports_service.dart';

/// خدمة تصدير التقارير إلى PDF
class PdfExportService {
  static final PdfExportService _instance = PdfExportService._internal();
  factory PdfExportService() => _instance;
  PdfExportService._internal();

  /// تصدير تقرير إلى PDF
  Future<String> exportReportToPdf(ReportData reportData) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخط العربي
      final arabicFont = await _loadArabicFont();

      // إضافة الصفحات
      await _addReportPages(pdf, reportData, arabicFont);

      // حفظ الملف
      return await _savePdfFile(pdf, reportData);
    } catch (e) {
      throw Exception('خطأ في تصدير PDF: ${e.toString()}');
    }
  }

  /// تحميل الخط العربي
  Future<pw.Font> _loadArabicFont() async {
    try {
      // محاولة تحميل خط عربي من الأصول
      final fontData =
          await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // في حالة عدم وجود الخط، استخدام الخط الافتراضي
      return pw.Font.helvetica();
    }
  }

  /// إضافة صفحات التقرير
  Future<void> _addReportPages(
    pw.Document pdf,
    ReportData reportData,
    pw.Font arabicFont,
  ) async {
    // الصفحة الأولى: الغلاف والإحصائيات
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
          bold: arabicFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              _buildHeader(reportData),
              pw.SizedBox(height: 30),
              _buildStatistics(reportData),
              pw.SizedBox(height: 30),
              _buildGroupedDataSummary(reportData),
            ],
          );
        },
      ),
    );

    // الصفحات التالية: قائمة المديونيات التفصيلية
    if (reportData.debts.isNotEmpty) {
      await _addDebtListPages(pdf, reportData, arabicFont);
    }
  }

  /// بناء رأس التقرير
  pw.Widget _buildHeader(ReportData reportData) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200),
        borderRadius: pw.BorderRadius.circular(10),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'تطبيق فقيه لإدارة المديونيات',
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            reportData.title,
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            reportData.subtitle,
            style: pw.TextStyle(
              fontSize: 16,
              color: PdfColors.grey700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'تاريخ التوليد: ${DateFormat('dd/MM/yyyy HH:mm', 'ar').format(reportData.generatedAt)}',
            style: pw.TextStyle(
              fontSize: 12,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات
  pw.Widget _buildStatistics(ReportData reportData) {
    final stats = reportData.statistics;

    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الإحصائيات العامة',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 15),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              _buildStatRow(
                  'إجمالي المديونيات', '${stats['totalCount']} مديونية'),
              _buildStatRow(
                  'إجمالي المبلغ', _formatCurrency(stats['totalAmount'])),
              _buildStatRow('المديونيات النشطة',
                  '${stats['activeCount']} (${_formatCurrency(stats['activeAmount'])})'),
              _buildStatRow('المديونيات المدفوعة',
                  '${stats['paidCount']} (${_formatCurrency(stats['paidAmount'])})'),
              _buildStatRow('متوسط المديونية',
                  _formatCurrency(stats['averageDebtAmount'])),
              _buildStatRow('نسبة المديونيات النشطة',
                  '${stats['activePercentage'].toStringAsFixed(1)}%'),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء صف إحصائية
  pw.TableRow _buildStatRow(String label, String value) {
    return pw.TableRow(
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            label,
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
        pw.Container(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            value,
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ],
    );
  }

  /// بناء ملخص البيانات المجمعة
  pw.Widget _buildGroupedDataSummary(ReportData reportData) {
    if (reportData.groupedData.isEmpty) {
      return pw.SizedBox.shrink();
    }

    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص التفاصيل',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 15),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              // رأس الجدول
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                children: [
                  _buildTableHeader('البيان'),
                  _buildTableHeader('عدد المديونيات'),
                  _buildTableHeader('إجمالي المبلغ'),
                ],
              ),
              // البيانات
              ...reportData.groupedData.take(10).map((group) {
                String name = '';
                if (group.containsKey('personName')) {
                  name = group['personName'];
                } else if (group.containsKey('periodName')) {
                  name = group['periodName'];
                } else if (group.containsKey('statusName')) {
                  name = group['statusName'];
                }

                return pw.TableRow(
                  children: [
                    _buildTableCell(name),
                    _buildTableCell('${group['debtCount']}'),
                    _buildTableCell(_formatCurrency(group['totalAmount'])),
                  ],
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رأس الجدول
  pw.Widget _buildTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.blue800,
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// إضافة صفحات قائمة المديونيات
  Future<void> _addDebtListPages(
    pw.Document pdf,
    ReportData reportData,
    pw.Font arabicFont,
  ) async {
    const itemsPerPage = 20;
    final totalPages = (reportData.debts.length / itemsPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * itemsPerPage;
      final endIndex =
          (startIndex + itemsPerPage).clamp(0, reportData.debts.length);
      final pageDebts = reportData.debts.sublist(startIndex, endIndex);

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: pw.ThemeData.withFont(
            base: arabicFont,
            bold: arabicFont,
          ),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'قائمة المديونيات التفصيلية - صفحة ${pageIndex + 1} من $totalPages',
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue800,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                _buildDebtsTable(pageDebts),
              ],
            );
          },
        ),
      );
    }
  }

  /// بناء جدول المديونيات
  pw.Widget _buildDebtsTable(List<Debt> debts) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FixedColumnWidth(30), // رقم
        1: const pw.FlexColumnWidth(2), // المبلغ
        2: const pw.FlexColumnWidth(2), // التاريخ
        3: const pw.FlexColumnWidth(1.5), // الحالة
        4: const pw.FlexColumnWidth(3), // الملاحظات
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableHeader('#'),
            _buildTableHeader('المبلغ'),
            _buildTableHeader('التاريخ'),
            _buildTableHeader('الحالة'),
            _buildTableHeader('الملاحظات'),
          ],
        ),
        // البيانات
        ...debts.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final debt = entry.value;

          return pw.TableRow(
            children: [
              _buildTableCell('$index'),
              _buildTableCell(debt.formattedAmount),
              _buildTableCell(debt.formattedDate),
              _buildTableCell(_getStatusText(debt.status)),
              _buildTableCell(debt.notes ?? '-'),
            ],
          );
        }),
      ],
    );
  }

  /// الحصول على نص الحالة
  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return 'غير محدد';
    }
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return '${formatter.format(amount)} ر.س';
  }

  /// حفظ ملف PDF
  Future<String> _savePdfFile(pw.Document pdf, ReportData reportData) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/reports');

      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final fileName = 'report_$timestamp.pdf';
      final filePath = '${reportsDir.path}/$fileName';

      final file = File(filePath);
      final pdfBytes = await pdf.save();

      await file.writeAsBytes(pdfBytes);

      return filePath;
    } catch (e) {
      throw Exception('خطأ في حفظ ملف PDF: ${e.toString()}');
    }
  }

  /// طباعة التقرير
  Future<void> printReport(ReportData reportData) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخط العربي
      final arabicFont = await _loadArabicFont();

      // إضافة الصفحات
      await _addReportPages(pdf, reportData, arabicFont);

      // طباعة المستند
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: reportData.title,
      );
    } catch (e) {
      throw Exception('خطأ في طباعة التقرير: ${e.toString()}');
    }
  }

  /// مشاركة التقرير
  Future<void> shareReport(ReportData reportData) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخط العربي
      final arabicFont = await _loadArabicFont();

      // إضافة الصفحات
      await _addReportPages(pdf, reportData, arabicFont);

      // مشاركة المستند
      await Printing.sharePdf(
        bytes: await pdf.save(),
        filename: '${reportData.title}.pdf',
      );
    } catch (e) {
      throw Exception('خطأ في مشاركة التقرير: ${e.toString()}');
    }
  }

  /// معاينة التقرير
  Future<void> previewReport(ReportData reportData) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخط العربي
      final arabicFont = await _loadArabicFont();

      // إضافة الصفحات
      await _addReportPages(pdf, reportData, arabicFont);

      // معاينة المستند
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: reportData.title,
      );
    } catch (e) {
      throw Exception('خطأ في معاينة التقرير: ${e.toString()}');
    }
  }

  /// إنشاء تقرير مخصص بقالب محدد
  Future<String> createCustomReport({
    required String title,
    required String subtitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? footerText,
  }) async {
    try {
      final pdf = pw.Document();
      final arabicFont = await _loadArabicFont();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: pw.ThemeData.withFont(
            base: arabicFont,
            bold: arabicFont,
          ),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    border: pw.Border.all(color: PdfColors.blue200),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        title,
                        style: pw.TextStyle(
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      if (subtitle.isNotEmpty) ...[
                        pw.SizedBox(height: 10),
                        pw.Text(
                          subtitle,
                          style: const pw.TextStyle(fontSize: 14),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ],
                    ],
                  ),
                ),

                pw.SizedBox(height: 30),

                // الجدول
                if (data.isNotEmpty)
                  pw.Table(
                    border: pw.TableBorder.all(color: PdfColors.grey300),
                    children: [
                      // رأس الجدول
                      pw.TableRow(
                        decoration:
                            const pw.BoxDecoration(color: PdfColors.grey100),
                        children: headers
                            .map((header) => _buildTableHeader(header))
                            .toList(),
                      ),
                      // البيانات
                      ...data.map((row) {
                        return pw.TableRow(
                          children: headers.map((header) {
                            final value = row[header]?.toString() ?? '-';
                            return _buildTableCell(value);
                          }).toList(),
                        );
                      }).toList(),
                    ],
                  ),

                // التذييل
                if (footerText != null) ...[
                  pw.Spacer(),
                  pw.Container(
                    width: double.infinity,
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      border: pw.Border(
                          top: pw.BorderSide(color: PdfColors.grey300)),
                    ),
                    child: pw.Text(
                      footerText,
                      style: const pw.TextStyle(fontSize: 10),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      );

      return await _savePdfFile(
          pdf,
          ReportData(
            title: title,
            subtitle: subtitle,
            generatedAt: DateTime.now(),
            type: ReportType.summary,
            period: ReportPeriod.all,
            debts: [],
            statistics: {},
            groupedData: [],
          ));
    } catch (e) {
      throw Exception('خطأ في إنشاء التقرير المخصص: ${e.toString()}');
    }
  }
}
