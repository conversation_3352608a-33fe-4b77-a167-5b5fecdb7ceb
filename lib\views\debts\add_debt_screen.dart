import 'package:flutter/material.dart';
import '../../models/debt.dart';
import '../../models/customer.dart';
import '../../models/employee.dart';
import '../../services/database/debts_service.dart';
import '../../services/database/customers_service.dart';
import '../../services/database/employees_service.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../widgets/common/form_field_widget.dart';
import '../../widgets/common/image_picker_widget.dart';
import '../../widgets/customers/customer_selection_card.dart';
import '../../widgets/employees/employee_selection_card.dart';

/// شاشة إضافة مديونية جديدة
class AddDebtScreen extends StatefulWidget {
  const AddDebtScreen({super.key});

  @override
  State<AddDebtScreen> createState() => _AddDebtScreenState();
}

class _AddDebtScreenState extends State<AddDebtScreen> {
  final _formKey = GlobalKey<FormState>();
  final DebtsService _debtsService = DebtsService();
  final CustomersService _customersService = CustomersService();
  final EmployeesService _employeesService = EmployeesService();

  // متحكمات النص
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  // حالة النموذج
  bool _isLoading = false;
  bool _isLoadingPersons = false;

  // بيانات المديونية
  String _personType = AppConstants.personTypeCustomer; // customer أو employee
  int? _selectedPersonId;
  String? _selectedPersonName;
  DateTime _selectedDate = DateTime.now();
  String? _imagePath;

  // قوائم الأشخاص
  List<Customer> _customers = [];
  List<Employee> _employees = [];

  // رسائل الخطأ
  String? _amountError;
  String? _personError;

  @override
  void initState() {
    super.initState();
    _loadPersons();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل قوائم العملاء والموظفين
  Future<void> _loadPersons() async {
    setState(() {
      _isLoadingPersons = true;
    });

    try {
      final customers = await _customersService.getAllCustomers();
      final employees = await _employeesService.getAllEmployees();

      setState(() {
        _customers = customers;
        _employees = employees;
        _isLoadingPersons = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPersons = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('إضافة مديونية جديدة'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // زر الحفظ
        TextButton(
          onPressed: _isLoading ? null : _saveDebt,
          child: Text(
            'حفظ',
            style: TextStyle(
              color: _isLoading ? Colors.white54 : Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة اختيار نوع الشخص
            _buildPersonTypeCard(),

            const SizedBox(height: 16),

            // بطاقة اختيار الشخص
            _buildPersonSelectionCard(),

            const SizedBox(height: 16),

            // بطاقة تفاصيل المديونية
            _buildDebtDetailsCard(),

            const SizedBox(height: 16),

            // بطاقة الصورة
            _buildImageCard(),

            const SizedBox(height: 24),

            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة اختيار نوع الشخص
  Widget _buildPersonTypeCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'نوع الشخص',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // خيارات نوع الشخص
            Row(
              children: [
                Expanded(
                  child: _buildPersonTypeOption(
                    title: 'عميل',
                    value: AppConstants.personTypeCustomer,
                    icon: Icons.person,
                    color: AppTheme.secondaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPersonTypeOption(
                    title: 'موظف',
                    value: AppConstants.personTypeEmployee,
                    icon: Icons.badge,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء خيار نوع الشخص
  Widget _buildPersonTypeOption({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _personType == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _personType = value;
          _selectedPersonId = null;
          _selectedPersonName = null;
          _personError = null;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isSelected ? color : Colors.grey[500],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? color : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة اختيار الشخص
  Widget _buildPersonSelectionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  _personType == AppConstants.personTypeCustomer
                      ? Icons.person_search
                      : Icons.badge_outlined,
                  color: _personType == AppConstants.personTypeCustomer
                      ? AppTheme.secondaryColor
                      : Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _personType == AppConstants.personTypeCustomer
                      ? 'اختيار العميل'
                      : 'اختيار الموظف',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _personType == AppConstants.personTypeCustomer
                        ? AppTheme.secondaryColor
                        : Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // عرض الشخص المختار أو قائمة الاختيار
            if (_selectedPersonId != null)
              _buildSelectedPersonCard()
            else
              _buildPersonSelectionList(),

            // رسالة الخطأ
            if (_personError != null) ...[
              const SizedBox(height: 8),
              Text(
                _personError!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الشخص المختار
  Widget _buildSelectedPersonCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // أيقونة الشخص
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              _personType == AppConstants.personTypeCustomer
                  ? Icons.person
                  : Icons.badge,
              color: Colors.white,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // اسم الشخص
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedPersonName ?? 'غير محدد',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                Text(
                  _personType == AppConstants.personTypeCustomer
                      ? 'عميل'
                      : 'موظف',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),

          // زر التغيير
          IconButton(
            onPressed: () {
              setState(() {
                _selectedPersonId = null;
                _selectedPersonName = null;
              });
            },
            icon: const Icon(
              Icons.change_circle,
              color: AppTheme.primaryColor,
            ),
            tooltip: 'تغيير الاختيار',
          ),
        ],
      ),
    );
  }

  /// بناء قائمة اختيار الشخص
  Widget _buildPersonSelectionList() {
    if (_isLoadingPersons) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    final persons = _personType == AppConstants.personTypeCustomer
        ? _customers
        : _employees;

    if (persons.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              _personType == AppConstants.personTypeCustomer
                  ? Icons.person_off
                  : Icons.badge_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              _personType == AppConstants.personTypeCustomer
                  ? 'لا يوجد عملاء'
                  : 'لا يوجد موظفين',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _personType == AppConstants.personTypeCustomer
                  ? 'يجب إضافة عملاء أولاً'
                  : 'يجب إضافة موظفين أولاً',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // عنوان القائمة
        Row(
          children: [
            Text(
              'اختر ${_personType == AppConstants.personTypeCustomer ? 'العميل' : 'الموظف'}:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const Spacer(),
            Text(
              '${persons.length} ${_personType == AppConstants.personTypeCustomer ? 'عميل' : 'موظف'}',
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // قائمة الأشخاص
        Container(
          constraints: const BoxConstraints(maxHeight: 200),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: persons.length,
            itemBuilder: (context, index) {
              if (_personType == AppConstants.personTypeCustomer) {
                final customer = persons[index] as Customer;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: CustomerSelectionCard(
                    customer: customer,
                    isSelected: _selectedPersonId == customer.id,
                    onTap: () => _selectPerson(customer.id!, customer.name),
                  ),
                );
              } else {
                final employee = persons[index] as Employee;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: EmployeeSelectionCard(
                    employee: employee,
                    isSelected: _selectedPersonId == employee.id,
                    onTap: () => _selectPerson(employee.id!, employee.name),
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  /// اختيار شخص
  void _selectPerson(int personId, String personName) {
    setState(() {
      _selectedPersonId = personId;
      _selectedPersonName = personName;
      _personError = null;
    });
  }

  /// بناء بطاقة تفاصيل المديونية
  Widget _buildDebtDetailsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Colors.green[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل المديونية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // حقل المبلغ
            FormFieldWidget(
              controller: _amountController,
              label: 'المبلغ (ريال سعودي)',
              hintText: 'أدخل مبلغ المديونية',
              prefixIcon: Icons.attach_money,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              validator: (value) => _amountError,
              onChanged: (_) => _validateAmount(),
              textInputAction: TextInputAction.next,
            ),

            const SizedBox(height: 16),

            // حقل التاريخ
            _buildDateField(),

            const SizedBox(height: 16),

            // حقل الملاحظات
            FormFieldWidget(
              controller: _notesController,
              label: 'الملاحظات (اختياري)',
              hintText: 'أدخل أي ملاحظات إضافية',
              prefixIcon: Icons.note,
              maxLines: 3,
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حقل التاريخ
  Widget _buildDateField() {
    return GestureDetector(
      onTap: _selectDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تاريخ المديونية',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الصورة
  Widget _buildImageCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.image,
                  color: Colors.purple[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'إرفاق صورة (اختياري)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // ويدجت اختيار الصورة
            ImagePickerWidget(
              imagePath: _imagePath,
              onImageChanged: (path) {
                setState(() {
                  _imagePath = path;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر الحفظ الرئيسي
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _saveDebt,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ المديونية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // زر الإلغاء
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            icon: const Icon(Icons.cancel_outlined),
            label: const Text('إلغاء'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.textSecondaryColor,
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// التحقق من صحة المبلغ
  void _validateAmount() {
    setState(() {
      _amountError = null;

      final amountText = _amountController.text.trim();
      if (amountText.isEmpty) {
        _amountError = 'المبلغ مطلوب';
        return;
      }

      final amount = double.tryParse(amountText);
      if (amount == null) {
        _amountError = 'المبلغ غير صحيح';
        return;
      }

      if (amount <= 0) {
        _amountError = 'المبلغ يجب أن يكون أكبر من صفر';
        return;
      }

      if (amount > 999999999) {
        _amountError = 'المبلغ كبير جداً';
        return;
      }
    });
  }

  /// اختيار التاريخ
  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      locale: const Locale('ar'),
    );

    if (pickedDate != null && pickedDate != _selectedDate) {
      setState(() {
        _selectedDate = pickedDate;
      });
    }
  }

  /// التحقق من صحة جميع الحقول
  bool _validateAllFields() {
    bool isValid = true;

    // التحقق من اختيار الشخص
    if (_selectedPersonId == null) {
      setState(() {
        _personError =
            'يجب اختيار ${_personType == AppConstants.personTypeCustomer ? 'عميل' : 'موظف'}';
      });
      isValid = false;
    }

    // التحقق من المبلغ
    _validateAmount();
    if (_amountError != null) {
      isValid = false;
    }

    return isValid;
  }

  /// حفظ المديونية
  Future<void> _saveDebt() async {
    if (!_validateAllFields()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تصحيح الأخطاء قبل الحفظ'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء مديونية جديدة
      final debt = Debt(
        personId: _selectedPersonId!,
        personType: _personType,
        amount: double.parse(_amountController.text.trim()),
        date: _selectedDate,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        imagePath: _imagePath,
      );

      // التحقق النهائي من صحة البيانات
      final validationErrors = debt.validate();
      if (validationErrors.isNotEmpty) {
        throw Exception(validationErrors.first);
      }

      // حفظ المديونية في قاعدة البيانات
      await _debtsService.addDebt(debt);

      if (mounted) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة مديونية بمبلغ ${debt.formattedAmount} '
                'لـ $_selectedPersonName بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للشاشة السابقة مع إشارة النجاح
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المديونية: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
