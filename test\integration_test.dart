import 'package:flutter_test/flutter_test.dart';
import 'package:faqeh/main.dart' as app;
import 'package:faqeh/models/customer.dart';
import 'package:faqeh/models/employee.dart';
import 'package:faqeh/models/debt.dart';
import 'package:faqeh/services/database/customers_service.dart';
import 'package:faqeh/services/database/employees_service.dart';
import 'package:faqeh/services/database/debts_service.dart';
import 'package:faqeh/services/image_service.dart';
import 'package:faqeh/constants/app_constants.dart';

void main() {
  group('تطبيق إدارة الديون - اختبارات التكامل', () {
    late CustomersService customersService;
    late EmployeesService employeesService;
    late DebtsService debtsService;
    late ImageService imageService;

    setUpAll(() {
      customersService = CustomersService();
      employeesService = EmployeesService();
      debtsService = DebtsService();
      imageService = ImageService();
    });

    group('اختبارات النماذج (Models)', () {
      test('نموذج العميل - التحقق من صحة البيانات', () {
        // عميل صحيح
        final validCustomer = Customer(
          name: 'أحمد محمد',
          phone: '0501234567',
          email: '<EMAIL>',
          address: 'الرياض، السعودية',
        );

        expect(validCustomer.isValid, isTrue);
        expect(validCustomer.validate(), isEmpty);
        expect(validCustomer.initials, equals('أم'));
        expect(validCustomer.hasContactInfo, isTrue);

        // عميل غير صحيح
        final invalidCustomer = Customer(
          name: '', // اسم فارغ
          phone: '123', // رقم هاتف غير صحيح
          email: 'invalid-email', // بريد إلكتروني غير صحيح
        );

        expect(invalidCustomer.isValid, isFalse);
        expect(invalidCustomer.validate(), isNotEmpty);
      });

      test('نموذج الموظف - التحقق من صحة البيانات', () {
        final validEmployee = Employee(
          name: 'فاطمة أحمد',
          phone: '0509876543',
          email: '<EMAIL>',
          position: 'مطورة برمجيات',
          department: 'تقنية المعلومات',
        );

        expect(validEmployee.isValid, isTrue);
        expect(validEmployee.validate(), isEmpty);
        expect(validEmployee.initials, equals('فأ'));
        expect(validEmployee.hasWorkInfo, isTrue);
      });

      test('نموذج المديونية - التحقق من صحة البيانات', () {
        final validDebt = Debt(
          personId: 1,
          personType: AppConstants.personTypeCustomer,
          amount: 1500.50,
          date: DateTime.now(),
          notes: 'مديونية شراء مواد',
        );

        expect(validDebt.isValid, isTrue);
        expect(validDebt.validate(), isEmpty);
        expect(validDebt.isActive, isTrue);
        expect(validDebt.isCustomerDebt, isTrue);
        expect(validDebt.formattedAmount, contains('1,500.50'));

        // مديونية غير صحيحة
        final invalidDebt = Debt(
          personId: 0, // معرف غير صحيح
          personType: 'invalid_type', // نوع غير صحيح
          amount: -100, // مبلغ سالب
          date: DateTime.now().add(const Duration(days: 10)), // تاريخ مستقبلي
        );

        expect(invalidDebt.isValid, isFalse);
        expect(invalidDebt.validate(), isNotEmpty);
      });
    });

    group('اختبارات الخدمات (Services)', () {
      test('خدمة الصور - التحقق من الأذونات', () async {
        final permissions = await imageService.checkAllImagePermissions();
        
        expect(permissions, isA<Map<String, bool>>());
        expect(permissions.containsKey('camera'), isTrue);
        expect(permissions.containsKey('gallery'), isTrue);
      });

      test('خدمة الصور - معلومات الصورة', () async {
        // اختبار مع مسار غير موجود
        final imageInfo = await imageService.getImageInfo('/path/to/nonexistent/image.jpg');
        expect(imageInfo, isNull);
      });
    });

    group('اختبارات قاعدة البيانات', () {
      test('خدمة العملاء - العمليات الأساسية', () async {
        // إضافة عميل جديد
        final customer = Customer(
          name: 'عميل اختبار',
          phone: '0501111111',
          email: '<EMAIL>',
        );

        try {
          final customerId = await customersService.addCustomer(customer);
          expect(customerId, greaterThan(0));

          // البحث عن العميل
          final foundCustomer = await customersService.getCustomerById(customerId);
          expect(foundCustomer, isNotNull);
          expect(foundCustomer!.name, equals('عميل اختبار'));

          // تحديث العميل
          final updatedCustomer = foundCustomer.copyWith(
            name: 'عميل محدث',
            phone: '0502222222',
          );
          
          final updateResult = await customersService.updateCustomer(updatedCustomer);
          expect(updateResult, greaterThan(0));

          // التحقق من التحديث
          final verifyCustomer = await customersService.getCustomerById(customerId);
          expect(verifyCustomer!.name, equals('عميل محدث'));
          expect(verifyCustomer.phone, equals('0502222222'));

          // حذف العميل
          final deleteResult = await customersService.deleteCustomer(customerId);
          expect(deleteResult, greaterThan(0));

          // التحقق من الحذف
          final deletedCustomer = await customersService.getCustomerById(customerId);
          expect(deletedCustomer, isNull);
        } catch (e) {
          fail('خطأ في اختبار خدمة العملاء: $e');
        }
      });

      test('خدمة الموظفين - العمليات الأساسية', () async {
        final employee = Employee(
          name: 'موظف اختبار',
          phone: '0503333333',
          position: 'مطور',
          department: 'التقنية',
        );

        try {
          final employeeId = await employeesService.addEmployee(employee);
          expect(employeeId, greaterThan(0));

          final foundEmployee = await employeesService.getEmployeeById(employeeId);
          expect(foundEmployee, isNotNull);
          expect(foundEmployee!.name, equals('موظف اختبار'));

          await employeesService.deleteEmployee(employeeId);
        } catch (e) {
          fail('خطأ في اختبار خدمة الموظفين: $e');
        }
      });

      test('خدمة المديونيات - العمليات الأساسية', () async {
        // إنشاء عميل للاختبار
        final customer = Customer(name: 'عميل للمديونية', phone: '0504444444');
        final customerId = await customersService.addCustomer(customer);

        final debt = Debt(
          personId: customerId,
          personType: AppConstants.personTypeCustomer,
          amount: 500.0,
          date: DateTime.now(),
          notes: 'مديونية اختبار',
        );

        try {
          final debtId = await debtsService.addDebt(debt);
          expect(debtId, greaterThan(0));

          final foundDebt = await debtsService.getDebtById(debtId);
          expect(foundDebt, isNotNull);
          expect(foundDebt!.amount, equals(500.0));

          // اختبار البحث بالشخص
          final personDebts = await debtsService.getDebtsByPersonId(
            customerId,
            AppConstants.personTypeCustomer,
          );
          expect(personDebts, isNotEmpty);
          expect(personDebts.first.id, equals(debtId));

          // تنظيف البيانات
          await debtsService.deleteDebt(debtId);
          await customersService.deleteCustomer(customerId);
        } catch (e) {
          fail('خطأ في اختبار خدمة المديونيات: $e');
        }
      });
    });

    group('اختبارات التكامل الشاملة', () {
      test('سيناريو كامل: إضافة عميل ومديونية', () async {
        try {
          // 1. إضافة عميل
          final customer = Customer(
            name: 'عميل التكامل',
            phone: '0505555555',
            email: '<EMAIL>',
          );
          final customerId = await customersService.addCustomer(customer);

          // 2. إضافة مديونية للعميل
          final debt = Debt(
            personId: customerId,
            personType: AppConstants.personTypeCustomer,
            amount: 1000.0,
            date: DateTime.now(),
            notes: 'مديونية تكامل',
          );
          final debtId = await debtsService.addDebt(debt);

          // 3. التحقق من الإحصائيات
          final stats = await debtsService.getDebtsStatistics();
          expect(stats['total_debts'], greaterThan(0));
          expect(stats['total_amount'], greaterThan(0));

          // 4. البحث في العملاء
          final searchResults = await customersService.searchCustomers('تكامل');
          expect(searchResults, isNotEmpty);

          // 5. تنظيف البيانات
          await debtsService.deleteDebt(debtId);
          await customersService.deleteCustomer(customerId);

          print('✅ اختبار التكامل الشامل نجح بنجاح');
        } catch (e) {
          fail('خطأ في اختبار التكامل الشامل: $e');
        }
      });
    });
  });
}
