/// ثوابت التطبيق الأساسية
class AppConstants {
  // معلومات التطبيق
  static const String appName = 'فقيه';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'تطبيق إدارة المديونية للعملاء والموظفين';

  // قاعدة البيانات
  static const String databaseName = 'faqeh_database.db';
  static const int databaseVersion = 1;

  // أسماء الجداول
  static const String customersTable = 'customers';
  static const String employeesTable = 'employees';
  static const String debtsTable = 'debts';

  // أنواع الأشخاص
  static const String personTypeCustomer = 'customer';
  static const String personTypeEmployee = 'employee';

  // حالات المديونية
  static const String debtStatusActive = 'active';
  static const String debtStatusPaid = 'paid';
  static const String debtStatusCancelled = 'cancelled';

  // أحجام الخط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 20.0;
  static const double fontSizeXLarge = 24.0;

  // المسافات
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // أحجام الأيقونات
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;

  // أحجام الأزرار
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 48.0;
  static const double buttonHeightLarge = 56.0;

  // نصوص التطبيق
  static const String customers = 'العملاء';
  static const String employees = 'الموظفون';
  static const String debts = 'المديونيات';
  static const String reports = 'التقارير';
  static const String home = 'الرئيسية';
  static const String add = 'إضافة';
  static const String edit = 'تعديل';
  static const String delete = 'حذف';
  static const String save = 'حفظ';
  static const String cancel = 'إلغاء';
  static const String search = 'بحث';
  static const String filter = 'تصفية';
  static const String sort = 'ترتيب';
  static const String export = 'تصدير';
  static const String print = 'طباعة';
  static const String name = 'الاسم';
  static const String phone = 'الهاتف';
  static const String email = 'البريد الإلكتروني';
  static const String address = 'العنوان';
  static const String position = 'المنصب';
  static const String department = 'القسم';
  static const String amount = 'المبلغ';
  static const String date = 'التاريخ';
  static const String notes = 'الملاحظات';
  static const String status = 'الحالة';
  static const String total = 'المجموع';
  static const String count = 'العدد';

  // رسائل التأكيد
  static const String confirmDelete = 'هل أنت متأكد من الحذف؟';
  static const String confirmSave = 'هل تريد حفظ التغييرات؟';
  static const String dataDeleted = 'تم حذف البيانات بنجاح';
  static const String dataSaved = 'تم حفظ البيانات بنجاح';
  static const String dataUpdated = 'تم تحديث البيانات بنجاح';

  // رسائل الخطأ
  static const String errorGeneral = 'حدث خطأ غير متوقع';
  static const String errorDatabase = 'خطأ في قاعدة البيانات';
  static const String errorValidation = 'يرجى التحقق من البيانات المدخلة';
  static const String errorRequired = 'هذا الحقل مطلوب';
  static const String errorInvalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String errorInvalidPhone = 'رقم الهاتف غير صحيح';
  static const String errorInvalidAmount = 'المبلغ غير صحيح';

  // تنسيقات التاريخ
  static const String dateFormatDisplay = 'dd/MM/yyyy';
  static const String dateTimeFormatDisplay = 'dd/MM/yyyy HH:mm';
  static const String dateFormatDatabase = 'yyyy-MM-dd HH:mm:ss';

  // إعدادات PDF
  static const String pdfTitle = 'تقرير المديونية';
  static const String pdfAuthor = 'تطبيق فقيه';
  static const String pdfSubject = 'تقرير مديونية العملاء والموظفين';

  // إعدادات الصور
  static const String imagesFolder = 'debt_images';
  static const int maxImageSizeKB = 500; // 500 KB
  static const int imageQuality = 80; // جودة الضغط
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png'];

  // نصوص متعلقة بالصور
  static const String takePhoto = 'التقاط صورة';
  static const String chooseFromGallery = 'اختيار من المعرض';
  static const String viewImage = 'عرض الصورة';
  static const String deleteImage = 'حذف الصورة';
  static const String imageAttached = 'تم إرفاق الصورة';
  static const String noImageAttached = 'لا توجد صورة مرفقة';
  static const String imageDeleted = 'تم حذف الصورة';
  static const String confirmDeleteImage = 'هل تريد حذف الصورة؟';

  // رسائل خطأ الصور
  static const String errorCameraPermission = 'لا يمكن الوصول للكاميرا';
  static const String errorGalleryPermission = 'لا يمكن الوصول للمعرض';
  static const String errorImageTooLarge = 'حجم الصورة كبير جداً';
  static const String errorUnsupportedFormat = 'تنسيق الصورة غير مدعوم';
  static const String errorImageProcessing = 'خطأ في معالجة الصورة';
}
