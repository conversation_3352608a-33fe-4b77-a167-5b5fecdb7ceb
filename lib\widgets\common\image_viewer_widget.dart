import 'dart:io';
import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';

/// ويدجت عرض الصور بشكل مكبر
class ImageViewerWidget extends StatefulWidget {
  final String imagePath;
  final String? title;
  final bool showDeleteButton;
  final VoidCallback? onDelete;

  const ImageViewerWidget({
    super.key,
    required this.imagePath,
    this.title,
    this.showDeleteButton = false,
    this.onDelete,
  });

  @override
  State<ImageViewerWidget> createState() => _ImageViewerWidgetState();
}

class _ImageViewerWidgetState extends State<ImageViewerWidget> {
  final TransformationController _transformationController = TransformationController();
  bool _isZoomed = false;

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  void _resetZoom() {
    _transformationController.value = Matrix4.identity();
    setState(() {
      _isZoomed = false;
    });
  }

  void _onInteractionUpdate(ScaleUpdateDetails details) {
    setState(() {
      _isZoomed = _transformationController.value.getMaxScaleOnAxis() > 1.0;
    });
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(AppConstants.confirmDeleteImage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(AppConstants.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDelete?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text(AppConstants.delete),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          widget.title ?? 'عرض الصورة',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          if (_isZoomed)
            IconButton(
              onPressed: _resetZoom,
              icon: const Icon(Icons.zoom_out_map),
              tooltip: 'إعادة تعيين التكبير',
            ),
          if (widget.showDeleteButton && widget.onDelete != null)
            IconButton(
              onPressed: _showDeleteConfirmation,
              icon: const Icon(Icons.delete),
              tooltip: AppConstants.deleteImage,
            ),
        ],
      ),
      body: Center(
        child: InteractiveViewer(
          transformationController: _transformationController,
          onInteractionUpdate: _onInteractionUpdate,
          minScale: 0.5,
          maxScale: 4.0,
          child: _buildImage(),
        ),
      ),
    );
  }

  Widget _buildImage() {
    final file = File(widget.imagePath);
    
    return FutureBuilder<bool>(
      future: file.exists(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
            ),
          );
        }

        if (snapshot.hasError || !snapshot.data!) {
          return _buildErrorWidget();
        }

        return Image.file(
          file,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorWidget();
          },
        );
      },
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.broken_image,
            size: 64,
            color: Colors.white54,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا يمكن عرض الصورة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'الصورة غير موجودة أو تالفة',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }
}

/// دالة مساعدة لعرض الصورة في شاشة منفصلة
void showImageViewer(
  BuildContext context, {
  required String imagePath,
  String? title,
  bool showDeleteButton = false,
  VoidCallback? onDelete,
}) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ImageViewerWidget(
        imagePath: imagePath,
        title: title,
        showDeleteButton: showDeleteButton,
        onDelete: onDelete,
      ),
    ),
  );
}

/// ويدجت مصغر لعرض الصورة مع إمكانية النقر للتكبير
class ImageThumbnailViewer extends StatelessWidget {
  final String imagePath;
  final double size;
  final String? title;
  final bool showDeleteButton;
  final VoidCallback? onDelete;

  const ImageThumbnailViewer({
    super.key,
    required this.imagePath,
    this.size = 100,
    this.title,
    this.showDeleteButton = false,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final file = File(imagePath);

    return GestureDetector(
      onTap: () => showImageViewer(
        context,
        imagePath: imagePath,
        title: title,
        showDeleteButton: showDeleteButton,
        onDelete: onDelete,
      ),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.primaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(7),
          child: FutureBuilder<bool>(
            future: file.exists(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (snapshot.hasError || !snapshot.data!) {
                return _buildErrorThumbnail();
              }

              return Image.file(
                file,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildErrorThumbnail();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildErrorThumbnail() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: Icon(
          Icons.broken_image,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }
}
