import '../../constants/app_constants.dart';
import '../../models/employee.dart';
import 'database_helper.dart';

/// خدمة إدارة الموظفين في قاعدة البيانات
class EmployeesService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة موظف جديد
  Future<int> addEmployee(Employee employee) async {
    final employeeMap = employee.toMap();
    employeeMap.remove('id'); // إزالة المعرف لأنه سيتم إنشاؤه تلقائياً
    return await _dbHelper.insert(AppConstants.employeesTable, employeeMap);
  }

  /// تحديث موظف موجود
  Future<int> updateEmployee(Employee employee) async {
    if (employee.id == null) {
      throw ArgumentError('Employee ID cannot be null for update operation');
    }
    
    final employeeMap = employee.toMap();
    return await _dbHelper.update(
      AppConstants.employeesTable,
      employeeMap,
      'id = ?',
      [employee.id],
    );
  }

  /// حذف موظف
  Future<int> deleteEmployee(int employeeId) async {
    return await _dbHelper.delete(
      AppConstants.employeesTable,
      'id = ?',
      [employeeId],
    );
  }

  /// الحصول على موظف بالمعرف
  Future<Employee?> getEmployeeById(int employeeId) async {
    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      where: 'id = ?',
      whereArgs: [employeeId],
    );

    if (results.isNotEmpty) {
      return Employee.fromMap(results.first);
    }
    return null;
  }

  /// الحصول على جميع الموظفين
  Future<List<Employee>> getAllEmployees({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      orderBy: orderBy ?? 'name ASC',
      limit: limit,
      offset: offset,
    );

    return results.map((map) => Employee.fromMap(map)).toList();
  }

  /// البحث في الموظفين
  Future<List<Employee>> searchEmployees(
    String searchTerm, {
    String? orderBy,
    int? limit,
  }) async {
    final results = await _dbHelper.search(
      AppConstants.employeesTable,
      searchTerm,
      searchColumns: ['name', 'phone', 'email', 'position', 'department'],
      orderBy: orderBy ?? 'name ASC',
      limit: limit,
    );

    return results.map((map) => Employee.fromMap(map)).toList();
  }

  /// الحصول على عدد الموظفين
  Future<int> getEmployeesCount() async {
    final results = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable}',
    );
    return results.first['count'] as int;
  }

  /// الحصول على الموظفين مع إجمالي مديونياتهم
  Future<List<Map<String, dynamic>>> getEmployeesWithDebts({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final sql = '''
      SELECT 
        e.*,
        COALESCE(SUM(CASE WHEN d.status = '${AppConstants.debtStatusActive}' THEN d.amount ELSE 0 END), 0) as total_active_debt,
        COALESCE(SUM(d.amount), 0) as total_debt,
        COUNT(d.id) as debt_count
      FROM ${AppConstants.employeesTable} e
      LEFT JOIN ${AppConstants.debtsTable} d ON e.id = d.person_id AND d.person_type = '${AppConstants.personTypeEmployee}'
      GROUP BY e.id
      ORDER BY ${orderBy ?? 'e.name ASC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    return await _dbHelper.rawQuery(sql);
  }

  /// الحصول على الموظفين الذين لديهم مديونيات نشطة
  Future<List<Employee>> getEmployeesWithActiveDebts({
    String? orderBy,
    int? limit,
  }) async {
    final sql = '''
      SELECT DISTINCT e.*
      FROM ${AppConstants.employeesTable} e
      INNER JOIN ${AppConstants.debtsTable} d ON e.id = d.person_id 
      WHERE d.person_type = '${AppConstants.personTypeEmployee}' 
        AND d.status = '${AppConstants.debtStatusActive}'
      ORDER BY ${orderBy ?? 'e.name ASC'}
      ${limit != null ? 'LIMIT $limit' : ''}
    ''';

    final results = await _dbHelper.rawQuery(sql);
    return results.map((map) => Employee.fromMap(map)).toList();
  }

  /// فرز الموظفين حسب معايير مختلفة
  Future<List<Employee>> getSortedEmployees({
    required String sortBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    String orderBy;
    
    switch (sortBy.toLowerCase()) {
      case 'name':
        orderBy = 'name ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'phone':
        orderBy = 'phone ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'email':
        orderBy = 'email ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'position':
        orderBy = 'position ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'department':
        orderBy = 'department ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'created_at':
        orderBy = 'created_at ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'updated_at':
        orderBy = 'updated_at ${ascending ? 'ASC' : 'DESC'}';
        break;
      default:
        orderBy = 'name ASC';
    }

    return await getAllEmployees(
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  /// الحصول على الموظفين حسب القسم
  Future<List<Employee>> getEmployeesByDepartment(
    String department, {
    String? orderBy,
    int? limit,
  }) async {
    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      where: 'LOWER(department) = LOWER(?)',
      whereArgs: [department],
      orderBy: orderBy ?? 'name ASC',
      limit: limit,
    );

    return results.map((map) => Employee.fromMap(map)).toList();
  }

  /// الحصول على الموظفين حسب المنصب
  Future<List<Employee>> getEmployeesByPosition(
    String position, {
    String? orderBy,
    int? limit,
  }) async {
    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      where: 'LOWER(position) = LOWER(?)',
      whereArgs: [position],
      orderBy: orderBy ?? 'name ASC',
      limit: limit,
    );

    return results.map((map) => Employee.fromMap(map)).toList();
  }

  /// الحصول على قائمة الأقسام المتاحة
  Future<List<String>> getDepartments() async {
    final results = await _dbHelper.rawQuery('''
      SELECT DISTINCT department 
      FROM ${AppConstants.employeesTable} 
      WHERE department IS NOT NULL AND department != ''
      ORDER BY department ASC
    ''');

    return results.map((map) => map['department'] as String).toList();
  }

  /// الحصول على قائمة المناصب المتاحة
  Future<List<String>> getPositions() async {
    final results = await _dbHelper.rawQuery('''
      SELECT DISTINCT position 
      FROM ${AppConstants.employeesTable} 
      WHERE position IS NOT NULL AND position != ''
      ORDER BY position ASC
    ''');

    return results.map((map) => map['position'] as String).toList();
  }

  /// التحقق من وجود موظف بنفس الاسم
  Future<bool> isEmployeeNameExists(String name, {int? excludeId}) async {
    String whereClause = 'LOWER(name) = LOWER(?)';
    List<dynamic> whereArgs = [name];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      where: whereClause,
      whereArgs: whereArgs,
      limit: 1,
    );

    return results.isNotEmpty;
  }

  /// التحقق من وجود موظف برقم هاتف معين
  Future<bool> isEmployeePhoneExists(String phone, {int? excludeId}) async {
    if (phone.isEmpty) return false;

    String whereClause = 'phone = ?';
    List<dynamic> whereArgs = [phone];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      where: whereClause,
      whereArgs: whereArgs,
      limit: 1,
    );

    return results.isNotEmpty;
  }

  /// التحقق من وجود موظف ببريد إلكتروني معين
  Future<bool> isEmployeeEmailExists(String email, {int? excludeId}) async {
    if (email.isEmpty) return false;

    String whereClause = 'LOWER(email) = LOWER(?)';
    List<dynamic> whereArgs = [email];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final results = await _dbHelper.query(
      AppConstants.employeesTable,
      where: whereClause,
      whereArgs: whereArgs,
      limit: 1,
    );

    return results.isNotEmpty;
  }
}
