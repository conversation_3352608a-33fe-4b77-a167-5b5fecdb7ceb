import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/customer.dart';
import '../../services/database/customers_service.dart';
import '../../widgets/common/form_field_widget.dart';

/// شاشة تعديل بيانات العميل
class EditCustomerScreen extends StatefulWidget {
  final Customer customer;

  const EditCustomerScreen({
    super.key,
    required this.customer,
  });

  @override
  State<EditCustomerScreen> createState() => _EditCustomerScreenState();
}

class _EditCustomerScreenState extends State<EditCustomerScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final CustomersService _customersService = CustomersService();

  // متحكمات النصوص
  late final TextEditingController _nameController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emailController;
  late final TextEditingController _addressController;

  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكمات بالبيانات الحالية
    _nameController = TextEditingController(text: widget.customer.name);
    _phoneController = TextEditingController(text: widget.customer.phone ?? '');
    _emailController = TextEditingController(text: widget.customer.email ?? '');
    _addressController =
        TextEditingController(text: widget.customer.address ?? '');

    // مراقبة التغييرات
    _nameController.addListener(_onFieldChanged);
    _phoneController.addListener(_onFieldChanged);
    _emailController.addListener(_onFieldChanged);
    _addressController.addListener(_onFieldChanged);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// مراقبة تغيير الحقول
  void _onFieldChanged() {
    final hasChanges = _nameController.text != widget.customer.name ||
        _phoneController.text != (widget.customer.phone ?? '') ||
        _emailController.text != (widget.customer.email ?? '') ||
        _addressController.text != (widget.customer.address ?? '');

    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  /// حفظ التعديلات
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_hasChanges) {
      Navigator.pop(context);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء العميل المحدث
      final updatedCustomer = widget.customer.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isNotEmpty
            ? _phoneController.text.trim()
            : null,
        email: _emailController.text.trim().isNotEmpty
            ? _emailController.text.trim()
            : null,
        address: _addressController.text.trim().isNotEmpty
            ? _addressController.text.trim()
            : null,
      );

      // التحقق من صحة البيانات
      final validationErrors = updatedCustomer.validate();
      if (validationErrors.isNotEmpty) {
        _showErrorDialog('خطأ في البيانات', validationErrors.join('\n'));
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // التحقق من عدم تكرار الاسم
      if (updatedCustomer.name != widget.customer.name) {
        final nameExists = await _customersService.isCustomerNameExists(
          updatedCustomer.name,
          excludeId: widget.customer.id,
        );
        if (nameExists) {
          _showErrorDialog('خطأ', 'يوجد عميل بنفس الاسم مسبقاً');
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      // التحقق من عدم تكرار رقم الهاتف
      if (updatedCustomer.phone != widget.customer.phone &&
          updatedCustomer.phone != null &&
          updatedCustomer.phone!.isNotEmpty) {
        final phoneExists = await _customersService.isCustomerPhoneExists(
          updatedCustomer.phone!,
          excludeId: widget.customer.id,
        );
        if (phoneExists) {
          _showErrorDialog('خطأ', 'يوجد عميل بنفس رقم الهاتف مسبقاً');
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      // التحقق من عدم تكرار البريد الإلكتروني
      if (updatedCustomer.email != widget.customer.email &&
          updatedCustomer.email != null &&
          updatedCustomer.email!.isNotEmpty) {
        final emailExists = await _customersService.isCustomerEmailExists(
          updatedCustomer.email!,
          excludeId: widget.customer.id,
        );
        if (emailExists) {
          _showErrorDialog('خطأ', 'يوجد عميل بنفس البريد الإلكتروني مسبقاً');
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      // حفظ التعديلات
      await _customersService.updateCustomer(updatedCustomer);

      // التحقق من أن الويدجت لا يزال مُحمل
      if (!mounted) return;

      // عرض رسالة نجاح والعودة
      _showSuccessSnackBar('تم تحديث بيانات العميل بنجاح');
      Navigator.pop(context, true);
    } catch (e) {
      _showErrorDialog('خطأ', 'حدث خطأ أثناء حفظ التعديلات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// التحقق من الخروج مع وجود تغييرات
  Future<bool> _onWillPop() async {
    if (!_hasChanges) {
      return true;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الخروج'),
        content:
            const Text('لديك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('البقاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('الخروج'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// التحقق من صحة الاسم
  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.errorRequired;
    }
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون أكثر من حرف واحد';
    }
    if (value.trim().length > 100) {
      return 'الاسم يجب أن يكون أقل من 100 حرف';
    }
    return null;
  }

  /// التحقق من صحة رقم الهاتف
  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final phoneRegex = RegExp(r'^[0-9+\-\s()]+$');
    if (!phoneRegex.hasMatch(value)) {
      return AppConstants.errorInvalidPhone;
    }

    if (value.length < 7 || value.length > 20) {
      return 'رقم الهاتف يجب أن يكون بين 7 و 20 رقم';
    }

    return null;
  }

  /// التحقق من صحة البريد الإلكتروني
  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return AppConstants.errorInvalidEmail;
    }

    return null;
  }

  /// التحقق من صحة العنوان
  String? _validateAddress(String? value) {
    if (value != null && value.length > 500) {
      return 'العنوان يجب أن يكون أقل من 500 حرف';
    }
    return null;
  }

  /// عرض رسالة خطأ
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldPop = await _onWillPop();
        if (shouldPop && context.mounted) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('تعديل بيانات العميل'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          actions: [
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              )
            else
              TextButton(
                onPressed: _hasChanges ? _saveChanges : null,
                child: Text(
                  AppConstants.save,
                  style: TextStyle(
                    color: _hasChanges ? Colors.white : Colors.white54,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات العميل الحالية
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: Text(
                                widget.customer.initials,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'تعديل بيانات: ${widget.customer.name}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                Text(
                                  'أضيف في ${widget.customer.formattedCreatedAt}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (_hasChanges) ...[
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.warningColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.edit,
                                size: 16,
                                color: AppTheme.warningColor,
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'يوجد تغييرات غير محفوظة',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.warningColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // حقل الاسم (مطلوب)
                FormFieldWidget(
                  label: '${AppConstants.name} *',
                  controller: _nameController,
                  validator: _validateName,
                  keyboardType: TextInputType.name,
                  textInputAction: TextInputAction.next,
                  prefixIcon: Icons.person,
                  enabled: !_isLoading,
                ),

                const SizedBox(height: 16),

                // حقل رقم الهاتف (اختياري)
                FormFieldWidget(
                  label: AppConstants.phone,
                  controller: _phoneController,
                  validator: _validatePhone,
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.next,
                  prefixIcon: Icons.phone,
                  enabled: !_isLoading,
                ),

                const SizedBox(height: 16),

                // حقل البريد الإلكتروني (اختياري)
                FormFieldWidget(
                  label: AppConstants.email,
                  controller: _emailController,
                  validator: _validateEmail,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  prefixIcon: Icons.email,
                  enabled: !_isLoading,
                ),

                const SizedBox(height: 16),

                // حقل العنوان (اختياري)
                FormFieldWidget(
                  label: AppConstants.address,
                  controller: _addressController,
                  validator: _validateAddress,
                  keyboardType: TextInputType.multiline,
                  textInputAction: TextInputAction.done,
                  prefixIcon: Icons.location_on,
                  maxLines: 3,
                  enabled: !_isLoading,
                ),

                const SizedBox(height: 32),

                // أزرار العمل
                Row(
                  children: [
                    // زر الإلغاء
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _isLoading
                            ? null
                            : () async {
                                final shouldPop = await _onWillPop();
                                if (shouldPop && context.mounted) {
                                  Navigator.pop(context);
                                }
                              },
                        icon: const Icon(Icons.cancel),
                        label: const Text(AppConstants.cancel),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // زر الحفظ
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed:
                            (_isLoading || !_hasChanges) ? null : _saveChanges,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Icon(Icons.save),
                        label: Text(
                            _isLoading ? 'جاري الحفظ...' : AppConstants.save),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
