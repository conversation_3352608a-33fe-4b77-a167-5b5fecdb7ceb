import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import '../../constants/app_theme.dart';
import '../../services/reports_service.dart';
import '../../services/pdf_export_service.dart';
import '../../models/debt.dart';

/// شاشة معاينة التقرير
class ReportPreviewScreen extends StatefulWidget {
  final ReportData reportData;

  const ReportPreviewScreen({
    super.key,
    required this.reportData,
  });

  @override
  State<ReportPreviewScreen> createState() => _ReportPreviewScreenState();
}

class _ReportPreviewScreenState extends State<ReportPreviewScreen> {
  final PdfExportService _pdfExportService = PdfExportService();
  final ReportsService _reportsService = ReportsService();
  bool _isExporting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.reportData.title),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // زر المشاركة
        IconButton(
          onPressed: _shareReport,
          icon: const Icon(Icons.share),
          tooltip: 'مشاركة',
        ),
        // زر الطباعة
        IconButton(
          onPressed: _printReport,
          icon: const Icon(Icons.print),
          tooltip: 'طباعة',
        ),
        // قائمة الخيارات
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export_pdf',
              child: Row(
                children: [
                  Icon(Icons.picture_as_pdf, color: Colors.red),
                  SizedBox(width: 8),
                  Text('تصدير PDF'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export_text',
              child: Row(
                children: [
                  Icon(Icons.text_snippet, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('تصدير نص'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'copy_text',
              child: Row(
                children: [
                  Icon(Icons.copy, color: Colors.green),
                  SizedBox(width: 8),
                  Text('نسخ النص'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader(),

          const SizedBox(height: 24),

          // الإحصائيات العامة
          _buildStatisticsSection(),

          const SizedBox(height: 24),

          // البيانات المجمعة
          if (widget.reportData.groupedData.isNotEmpty) ...[
            _buildGroupedDataSection(),
            const SizedBox(height: 24),
          ],

          // قائمة المديونيات
          if (widget.reportData.debts.isNotEmpty) _buildDebtsListSection(),
        ],
      ),
    );
  }

  /// بناء رأس التقرير
  Widget _buildReportHeader() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withOpacity(0.1),
              AppTheme.secondaryColor.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.reportData.title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.reportData.subtitle,
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  'تم التوليد: ${_formatDateTime(widget.reportData.generatedAt)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatisticsSection() {
    final stats = widget.reportData.statistics;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإحصائيات العامة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildStatCard(
                  'إجمالي المديونيات',
                  '${stats['totalCount']}',
                  Icons.receipt_long,
                  Colors.blue,
                ),
                _buildStatCard(
                  'إجمالي المبلغ',
                  _formatCurrency(stats['totalAmount']),
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildStatCard(
                  'المديونيات النشطة',
                  '${stats['activeCount']}',
                  Icons.pending,
                  Colors.orange,
                ),
                _buildStatCard(
                  'المديونيات المدفوعة',
                  '${stats['paidCount']}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ],
            ),
            const SizedBox(height: 16),
            // إحصائيات إضافية
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildStatRow(
                    'المبلغ النشط',
                    _formatCurrency(stats['activeAmount']),
                  ),
                  const Divider(),
                  _buildStatRow(
                    'المبلغ المدفوع',
                    _formatCurrency(stats['paidAmount']),
                  ),
                  const Divider(),
                  _buildStatRow(
                    'متوسط المديونية',
                    _formatCurrency(stats['averageDebtAmount']),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 10,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  /// بناء قسم البيانات المجمعة
  Widget _buildGroupedDataSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل التقرير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.reportData.groupedData.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final group = widget.reportData.groupedData[index];
                return _buildGroupItem(group);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر مجموعة
  Widget _buildGroupItem(Map<String, dynamic> group) {
    String title = '';
    String subtitle = '';

    if (group.containsKey('personName')) {
      title = group['personName'];
      subtitle =
          'عدد المديونيات: ${group['debtCount']} | المبلغ النشط: ${_formatCurrency(group['activeAmount'] ?? 0)}';
    } else if (group.containsKey('periodName')) {
      title = group['periodName'];
      subtitle = 'عدد المديونيات: ${group['debtCount']}';
    } else if (group.containsKey('statusName')) {
      title = group['statusName'];
      subtitle = 'عدد المديونيات: ${group['debtCount']}';
    }

    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: Text(
        _formatCurrency(group['totalAmount']),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  /// بناء قسم قائمة المديونيات
  Widget _buildDebtsListSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'قائمة المديونيات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                Text(
                  '${widget.reportData.debts.length} مديونية',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.reportData.debts.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final debt = widget.reportData.debts[index];
                return _buildDebtItem(debt, index + 1);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر مديونية
  Widget _buildDebtItem(Debt debt, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // الرقم
          Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: Text(
                '$index',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // التفاصيل
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      debt.formattedAmount,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getStatusColor(debt.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusText(debt.status),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(debt.status),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  debt.formattedDate,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    debt.notes!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                      fontStyle: FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الشريط السفلي
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر تصدير PDF
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isExporting ? null : _exportToPdf,
              icon: _isExporting
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.picture_as_pdf),
              label: Text(_isExporting ? 'جاري التصدير...' : 'تصدير PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // زر المشاركة
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _shareReport,
              icon: const Icon(Icons.share),
              label: const Text('مشاركة'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: const BorderSide(color: AppTheme.primaryColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return '${formatter.format(amount)} ر.س';
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final formatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');
    return formatter.format(dateTime);
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return 'غير محدد';
    }
  }

  /// تصدير إلى PDF
  Future<void> _exportToPdf() async {
    setState(() {
      _isExporting = true;
    });

    try {
      final filePath =
          await _pdfExportService.exportReportToPdf(widget.reportData);

      if (mounted) {
        setState(() {
          _isExporting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'مشاركة',
              onPressed: () => _shareFile(filePath),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// طباعة التقرير
  Future<void> _printReport() async {
    try {
      await _pdfExportService.printReport(widget.reportData);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طباعة التقرير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// مشاركة التقرير
  Future<void> _shareReport() async {
    try {
      await _pdfExportService.shareReport(widget.reportData);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة التقرير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// مشاركة ملف
  Future<void> _shareFile(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة الملف: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// معالجة إجراءات القائمة
  Future<void> _handleMenuAction(String action) async {
    switch (action) {
      case 'export_pdf':
        await _exportToPdf();
        break;
      case 'export_text':
        await _exportToText();
        break;
      case 'copy_text':
        await _copyToClipboard();
        break;
    }
  }

  /// تصدير إلى نص
  Future<void> _exportToText() async {
    try {
      final filePath =
          await _reportsService.saveReportAsTextFile(widget.reportData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير النصي بنجاح إلى: $filePath'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'مشاركة',
              onPressed: () => _shareFile(filePath),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير النصي: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// نسخ إلى الحافظة
  Future<void> _copyToClipboard() async {
    try {
      final textContent = _reportsService.exportReportAsText(widget.reportData);
      await Clipboard.setData(ClipboardData(text: textContent));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ التقرير إلى الحافظة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نسخ التقرير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
