import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/customer.dart';
import '../../services/database/customers_service.dart';
import '../../widgets/common/advanced_search_widget.dart';
import '../../widgets/customers/customer_card.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';
import 'edit_customer_screen.dart';

/// شاشة قائمة العملاء
class CustomersListScreen extends StatefulWidget {
  const CustomersListScreen({super.key});

  @override
  State<CustomersListScreen> createState() => _CustomersListScreenState();
}

class _CustomersListScreenState extends State<CustomersListScreen> {
  final CustomersService _customersService = CustomersService();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  
  List<Map<String, dynamic>> _customers = [];
  List<Map<String, dynamic>> _filteredCustomers = [];
  bool _isLoading = true;
  bool _isSearching = false;
  String _searchQuery = '';
  String _currentSortBy = 'name';
  bool _ascending = true;
  
  // خيارات الفرز
  final List<SortOption> _sortOptions = [
    const SortOption(value: 'name', label: 'الاسم', defaultAscending: true),
    const SortOption(value: 'created_at', label: 'تاريخ الإضافة', defaultAscending: false),
    const SortOption(value: 'phone', label: 'الهاتف', defaultAscending: true),
    const SortOption(value: 'email', label: 'البريد الإلكتروني', defaultAscending: true),
  ];

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// تحميل العملاء مع معلومات المديونيات
  Future<void> _loadCustomers() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final customers = await _customersService.getCustomersWithDebts(
        orderBy: _buildOrderBy(),
      );

      setState(() {
        _customers = customers;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل العملاء: $e');
    }
  }

  /// تطبيق الفلاتر والبحث
  void _applyFilters() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredCustomers = List.from(_customers);
      } else {
        _filteredCustomers = _customers.where((customerData) {
          final customer = Customer.fromMap(customerData);
          return customer.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                 (customer.phone?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                 (customer.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                 (customer.address?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
        }).toList();
      }
    });
  }

  /// بناء شرط الترتيب
  String _buildOrderBy() {
    final direction = _ascending ? 'ASC' : 'DESC';
    switch (_currentSortBy) {
      case 'name':
        return 'c.name $direction';
      case 'created_at':
        return 'c.created_at $direction';
      case 'phone':
        return 'c.phone $direction';
      case 'email':
        return 'c.email $direction';
      default:
        return 'c.name ASC';
    }
  }

  /// البحث في العملاء
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });
    _applyFilters();
  }

  /// تغيير خيارات الفرز
  void _onSortChanged(String sortBy, bool ascending) {
    setState(() {
      _currentSortBy = sortBy;
      _ascending = ascending;
    });
    _loadCustomers();
  }

  /// تحديث القائمة
  void _onRefresh() async {
    await _loadCustomers();
    _refreshController.refreshCompleted();
  }

  /// حذف عميل
  void _deleteCustomer(Customer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العميل "${customer.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(AppConstants.cancel),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDelete(customer);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text(AppConstants.delete),
          ),
        ],
      ),
    );
  }

  /// تنفيذ الحذف
  Future<void> _performDelete(Customer customer) async {
    try {
      await _customersService.deleteCustomer(customer.id!);
      _showSuccessSnackBar('تم حذف العميل بنجاح');
      _loadCustomers();
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف العميل: $e');
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.customers),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddCustomerScreen(),
                ),
              );
              if (result == true) {
                _loadCustomers();
              }
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // ويدجت البحث
          AdvancedSearchWidget(
            onSearchChanged: _onSearchChanged,
            hintText: 'البحث في العملاء...',
            onClearSearch: () {
              setState(() {
                _searchQuery = '';
                _isSearching = false;
              });
              _applyFilters();
            },
          ),

          // خيارات الفرز
          SortOptionsWidget(
            currentSortBy: _currentSortBy,
            ascending: _ascending,
            sortOptions: _sortOptions,
            onSortChanged: _onSortChanged,
          ),

          const SizedBox(height: 8),

          // نتائج البحث
          SearchResultsWidget(
            totalResults: _filteredCustomers.length,
            searchQuery: _isSearching ? _searchQuery : null,
            isLoading: _isLoading,
          ),

          // قائمة العملاء
          Expanded(
            child: _buildCustomersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_filteredCustomers.isEmpty) {
      return EmptySearchWidget(
        searchQuery: _isSearching ? _searchQuery : null,
        onClearSearch: _isSearching ? () {
          setState(() {
            _searchQuery = '';
            _isSearching = false;
          });
          _applyFilters();
        } : null,
      );
    }

    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      header: const WaterDropMaterialHeader(
        backgroundColor: AppTheme.primaryColor,
        color: Colors.white,
      ),
      child: AnimationLimiter(
        child: ListView.builder(
          itemCount: _filteredCustomers.length,
          itemBuilder: (context, index) {
            final customerData = _filteredCustomers[index];
            final customer = Customer.fromMap(customerData);

            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: CustomerCard(
                    customer: customer,
                    debtInfo: customerData,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => CustomerDetailsScreen(
                            customer: customer,
                          ),
                        ),
                      );
                    },
                    onEdit: () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditCustomerScreen(
                            customer: customer,
                          ),
                        ),
                      );
                      if (result == true) {
                        _loadCustomers();
                      }
                    },
                    onDelete: () => _deleteCustomer(customer),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
