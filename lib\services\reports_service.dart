import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/debt.dart';
import '../services/database/debts_service.dart';
import '../services/database/customers_service.dart';
import '../services/database/employees_service.dart';

/// أنواع التقارير المتاحة
enum ReportType {
  comprehensive, // تقرير شامل
  byCustomer, // تقرير بالعميل
  byEmployee, // تقرير بالموظف
  activeDebts, // المديونيات النشطة
  paidDebts, // المديونيات المدفوعة
  summary, // تقرير ملخص
}

/// فترات التقرير
enum ReportPeriod {
  today, // اليوم
  thisWeek, // هذا الأسبوع
  thisMonth, // هذا الشهر
  lastMonth, // الشهر الماضي
  thisYear, // هذا العام
  custom, // فترة مخصصة
  all, // جميع الفترات
}

/// بيانات التقرير
class ReportData {
  final String title;
  final String subtitle;
  final DateTime generatedAt;
  final ReportType type;
  final ReportPeriod period;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<Debt> debts;
  final Map<String, dynamic> statistics;
  final List<Map<String, dynamic>> groupedData;

  ReportData({
    required this.title,
    required this.subtitle,
    required this.generatedAt,
    required this.type,
    required this.period,
    this.startDate,
    this.endDate,
    required this.debts,
    required this.statistics,
    required this.groupedData,
  });
}

/// خدمة توليد التقارير
class ReportsService {
  static final ReportsService _instance = ReportsService._internal();
  factory ReportsService() => _instance;
  ReportsService._internal();

  final DebtsService _debtsService = DebtsService();
  final CustomersService _customersService = CustomersService();
  final EmployeesService _employeesService = EmployeesService();

  /// توليد تقرير
  Future<ReportData> generateReport({
    required ReportType type,
    required ReportPeriod period,
    DateTime? startDate,
    DateTime? endDate,
    int? specificPersonId,
    String? specificPersonType,
  }) async {
    // تحديد الفترة الزمنية
    final dateRange = _getDateRange(period, startDate, endDate);

    // تحميل البيانات
    List<Debt> debts = await _loadDebtsForReport(
      type: type,
      startDate: dateRange['start'],
      endDate: dateRange['end'],
      personId: specificPersonId,
      personType: specificPersonType,
    );

    // حساب الإحصائيات
    final statistics = await _calculateStatistics(debts);

    // تجميع البيانات
    final groupedData = await _groupData(debts, type);

    // إنشاء عنوان التقرير
    final title = _getReportTitle(type);
    final subtitle =
        _getReportSubtitle(period, dateRange['start'], dateRange['end']);

    return ReportData(
      title: title,
      subtitle: subtitle,
      generatedAt: DateTime.now(),
      type: type,
      period: period,
      startDate: dateRange['start'],
      endDate: dateRange['end'],
      debts: debts,
      statistics: statistics,
      groupedData: groupedData,
    );
  }

  /// تحديد نطاق التاريخ
  Map<String, DateTime?> _getDateRange(
      ReportPeriod period, DateTime? customStart, DateTime? customEnd) {
    final now = DateTime.now();
    DateTime? start, end;

    switch (period) {
      case ReportPeriod.today:
        start = DateTime(now.year, now.month, now.day);
        end = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case ReportPeriod.thisWeek:
        final weekday = now.weekday;
        start = now.subtract(Duration(days: weekday - 1));
        start = DateTime(start.year, start.month, start.day);
        end = start
            .add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
        break;
      case ReportPeriod.thisMonth:
        start = DateTime(now.year, now.month, 1);
        end = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case ReportPeriod.lastMonth:
        start = DateTime(now.year, now.month - 1, 1);
        end = DateTime(now.year, now.month, 0, 23, 59, 59);
        break;
      case ReportPeriod.thisYear:
        start = DateTime(now.year, 1, 1);
        end = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case ReportPeriod.custom:
        start = customStart;
        end = customEnd;
        break;
      case ReportPeriod.all:
        start = null;
        end = null;
        break;
    }

    return {'start': start, 'end': end};
  }

  /// تحميل المديونيات للتقرير
  Future<List<Debt>> _loadDebtsForReport({
    required ReportType type,
    DateTime? startDate,
    DateTime? endDate,
    int? personId,
    String? personType,
  }) async {
    List<Debt> debts = [];

    switch (type) {
      case ReportType.comprehensive:
        if (startDate != null && endDate != null) {
          debts = await _debtsService.getDebtsByDateRange(startDate, endDate);
        } else {
          debts = await _debtsService.getAllDebts();
        }
        break;
      case ReportType.byCustomer:
        if (personId != null) {
          debts = await _debtsService.getDebtsByPerson(personId, 'customer');
        } else {
          if (startDate != null && endDate != null) {
            debts = await _debtsService.getDebtsByDateRange(startDate, endDate,
                personType: 'customer');
          } else {
            debts = await _debtsService.getAllDebts();
            debts =
                debts.where((debt) => debt.personType == 'customer').toList();
          }
        }
        break;
      case ReportType.byEmployee:
        if (personId != null) {
          debts = await _debtsService.getDebtsByPerson(personId, 'employee');
        } else {
          if (startDate != null && endDate != null) {
            debts = await _debtsService.getDebtsByDateRange(startDate, endDate,
                personType: 'employee');
          } else {
            debts = await _debtsService.getAllDebts();
            debts =
                debts.where((debt) => debt.personType == 'employee').toList();
          }
        }
        break;
      case ReportType.activeDebts:
        if (startDate != null && endDate != null) {
          debts = await _debtsService.getDebtsByDateRange(startDate, endDate,
              status: 'active');
        } else {
          debts = await _debtsService.getActiveDebts();
        }
        break;
      case ReportType.paidDebts:
        if (startDate != null && endDate != null) {
          debts = await _debtsService.getDebtsByDateRange(startDate, endDate,
              status: 'paid');
        } else {
          debts = await _debtsService.getAllDebts();
          debts = debts.where((debt) => debt.status == 'paid').toList();
        }
        break;
      case ReportType.summary:
        if (startDate != null && endDate != null) {
          debts = await _debtsService.getDebtsByDateRange(startDate, endDate);
        } else {
          debts = await _debtsService.getAllDebts();
        }
        break;
    }

    return debts;
  }

  /// حساب الإحصائيات
  Future<Map<String, dynamic>> _calculateStatistics(List<Debt> debts) async {
    double totalAmount = 0;
    double activeAmount = 0;
    double paidAmount = 0;
    int totalCount = debts.length;
    int activeCount = 0;
    int paidCount = 0;
    int customersCount = 0;
    int employeesCount = 0;

    final customerIds = <int>{};
    final employeeIds = <int>{};

    for (final debt in debts) {
      totalAmount += debt.amount;

      if (debt.isActive) {
        activeAmount += debt.amount;
        activeCount++;
      } else if (debt.isPaid) {
        paidAmount += debt.amount;
        paidCount++;
      }

      if (debt.isCustomerDebt) {
        customerIds.add(debt.personId);
      } else if (debt.isEmployeeDebt) {
        employeeIds.add(debt.personId);
      }
    }

    customersCount = customerIds.length;
    employeesCount = employeeIds.length;

    return {
      'totalAmount': totalAmount,
      'activeAmount': activeAmount,
      'paidAmount': paidAmount,
      'totalCount': totalCount,
      'activeCount': activeCount,
      'paidCount': paidCount,
      'customersCount': customersCount,
      'employeesCount': employeesCount,
      'averageDebtAmount': totalCount > 0 ? totalAmount / totalCount : 0,
      'activePercentage': totalCount > 0 ? (activeCount / totalCount) * 100 : 0,
      'paidPercentage': totalCount > 0 ? (paidCount / totalCount) * 100 : 0,
    };
  }

  /// تجميع البيانات
  Future<List<Map<String, dynamic>>> _groupData(
      List<Debt> debts, ReportType type) async {
    final groupedData = <Map<String, dynamic>>[];

    switch (type) {
      case ReportType.byCustomer:
      case ReportType.byEmployee:
        final personGroups = <int, List<Debt>>{};

        for (final debt in debts) {
          personGroups.putIfAbsent(debt.personId, () => []).add(debt);
        }

        for (final entry in personGroups.entries) {
          final personId = entry.key;
          final personDebts = entry.value;

          String personName = 'غير محدد';
          try {
            if (type == ReportType.byCustomer) {
              final customer =
                  await _customersService.getCustomerById(personId);
              personName = customer?.name ?? 'عميل محذوف';
            } else {
              final employee =
                  await _employeesService.getEmployeeById(personId);
              personName = employee?.name ?? 'موظف محذوف';
            }
          } catch (e) {
            personName =
                type == ReportType.byCustomer ? 'عميل محذوف' : 'موظف محذوف';
          }

          final totalAmount =
              personDebts.fold(0.0, (sum, debt) => sum + debt.amount);
          final activeAmount = personDebts
              .where((d) => d.isActive)
              .fold(0.0, (sum, debt) => sum + debt.amount);

          groupedData.add({
            'personId': personId,
            'personName': personName,
            'debts': personDebts,
            'totalAmount': totalAmount,
            'activeAmount': activeAmount,
            'debtCount': personDebts.length,
            'activeCount': personDebts.where((d) => d.isActive).length,
          });
        }
        break;

      case ReportType.comprehensive:
      case ReportType.summary:
        // تجميع حسب الشهر
        final monthGroups = <String, List<Debt>>{};

        for (final debt in debts) {
          final monthKey = DateFormat('yyyy-MM').format(debt.date);
          monthGroups.putIfAbsent(monthKey, () => []).add(debt);
        }

        for (final entry in monthGroups.entries) {
          final monthKey = entry.key;
          final monthDebts = entry.value;
          final totalAmount =
              monthDebts.fold(0.0, (sum, debt) => sum + debt.amount);

          groupedData.add({
            'period': monthKey,
            'periodName': DateFormat('MMMM yyyy', 'ar')
                .format(DateTime.parse('$monthKey-01')),
            'debts': monthDebts,
            'totalAmount': totalAmount,
            'debtCount': monthDebts.length,
          });
        }
        break;

      default:
        // تجميع بسيط حسب الحالة
        final statusGroups = <String, List<Debt>>{};

        for (final debt in debts) {
          statusGroups.putIfAbsent(debt.status, () => []).add(debt);
        }

        for (final entry in statusGroups.entries) {
          final status = entry.key;
          final statusDebts = entry.value;
          final totalAmount =
              statusDebts.fold(0.0, (sum, debt) => sum + debt.amount);

          String statusName;
          switch (status) {
            case 'active':
              statusName = 'نشطة';
              break;
            case 'paid':
              statusName = 'مدفوعة';
              break;
            case 'cancelled':
              statusName = 'ملغاة';
              break;
            default:
              statusName = 'غير محدد';
          }

          groupedData.add({
            'status': status,
            'statusName': statusName,
            'debts': statusDebts,
            'totalAmount': totalAmount,
            'debtCount': statusDebts.length,
          });
        }
    }

    return groupedData;
  }

  /// الحصول على عنوان التقرير
  String _getReportTitle(ReportType type) {
    switch (type) {
      case ReportType.comprehensive:
        return 'التقرير الشامل للمديونيات';
      case ReportType.byCustomer:
        return 'تقرير مديونيات العملاء';
      case ReportType.byEmployee:
        return 'تقرير مديونيات الموظفين';
      case ReportType.activeDebts:
        return 'تقرير المديونيات النشطة';
      case ReportType.paidDebts:
        return 'تقرير المديونيات المدفوعة';
      case ReportType.summary:
        return 'التقرير الملخص';
    }
  }

  /// الحصول على العنوان الفرعي للتقرير
  String _getReportSubtitle(
      ReportPeriod period, DateTime? startDate, DateTime? endDate) {
    final formatter = DateFormat('dd/MM/yyyy', 'ar');

    switch (period) {
      case ReportPeriod.today:
        return 'تقرير اليوم - ${formatter.format(DateTime.now())}';
      case ReportPeriod.thisWeek:
        return 'تقرير هذا الأسبوع';
      case ReportPeriod.thisMonth:
        return 'تقرير هذا الشهر - ${DateFormat('MMMM yyyy', 'ar').format(DateTime.now())}';
      case ReportPeriod.lastMonth:
        final lastMonth = DateTime.now().subtract(const Duration(days: 30));
        return 'تقرير الشهر الماضي - ${DateFormat('MMMM yyyy', 'ar').format(lastMonth)}';
      case ReportPeriod.thisYear:
        return 'تقرير هذا العام - ${DateTime.now().year}';
      case ReportPeriod.custom:
        if (startDate != null && endDate != null) {
          return 'من ${formatter.format(startDate)} إلى ${formatter.format(endDate)}';
        }
        return 'فترة مخصصة';
      case ReportPeriod.all:
        return 'جميع الفترات';
    }
  }

  /// تصدير التقرير كنص
  String exportReportAsText(ReportData reportData) {
    final buffer = StringBuffer();
    final formatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');

    // رأس التقرير
    buffer.writeln('=' * 50);
    buffer.writeln(reportData.title);
    buffer.writeln(reportData.subtitle);
    buffer
        .writeln('تاريخ التوليد: ${formatter.format(reportData.generatedAt)}');
    buffer.writeln('=' * 50);
    buffer.writeln();

    // الإحصائيات العامة
    buffer.writeln('الإحصائيات العامة:');
    buffer.writeln('-' * 20);
    buffer.writeln('إجمالي المديونيات: ${reportData.statistics['totalCount']}');
    buffer.writeln(
        'إجمالي المبلغ: ${_formatCurrency(reportData.statistics['totalAmount'])}');
    buffer.writeln(
        'المديونيات النشطة: ${reportData.statistics['activeCount']} (${_formatCurrency(reportData.statistics['activeAmount'])})');
    buffer.writeln(
        'المديونيات المدفوعة: ${reportData.statistics['paidCount']} (${_formatCurrency(reportData.statistics['paidAmount'])})');
    buffer.writeln(
        'متوسط المديونية: ${_formatCurrency(reportData.statistics['averageDebtAmount'])}');
    buffer.writeln();

    // البيانات المجمعة
    if (reportData.groupedData.isNotEmpty) {
      buffer.writeln('التفاصيل:');
      buffer.writeln('-' * 20);

      for (final group in reportData.groupedData) {
        if (group.containsKey('personName')) {
          buffer.writeln('${group['personName']}:');
          buffer.writeln('  عدد المديونيات: ${group['debtCount']}');
          buffer.writeln(
              '  إجمالي المبلغ: ${_formatCurrency(group['totalAmount'])}');
          buffer.writeln(
              '  المبلغ النشط: ${_formatCurrency(group['activeAmount'])}');
        } else if (group.containsKey('periodName')) {
          buffer.writeln('${group['periodName']}:');
          buffer.writeln('  عدد المديونيات: ${group['debtCount']}');
          buffer.writeln(
              '  إجمالي المبلغ: ${_formatCurrency(group['totalAmount'])}');
        } else if (group.containsKey('statusName')) {
          buffer.writeln('${group['statusName']}:');
          buffer.writeln('  عدد المديونيات: ${group['debtCount']}');
          buffer.writeln(
              '  إجمالي المبلغ: ${_formatCurrency(group['totalAmount'])}');
        }
        buffer.writeln();
      }
    }

    // قائمة المديونيات التفصيلية
    if (reportData.debts.isNotEmpty) {
      buffer.writeln('قائمة المديونيات التفصيلية:');
      buffer.writeln('-' * 30);

      for (int i = 0; i < reportData.debts.length; i++) {
        final debt = reportData.debts[i];
        buffer.writeln(
            '${i + 1}. ${debt.formattedAmount} - ${debt.formattedDate} - ${debt.statusText}');
        if (debt.notes != null && debt.notes!.isNotEmpty) {
          buffer.writeln('   ملاحظة: ${debt.notes}');
        }
      }
    }

    buffer.writeln();
    buffer.writeln('=' * 50);
    buffer.writeln('تم توليد هذا التقرير بواسطة تطبيق فقيه لإدارة المديونيات');

    return buffer.toString();
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return '${formatter.format(amount)} ر.س';
  }

  /// حفظ التقرير كملف نصي
  Future<String> saveReportAsTextFile(ReportData reportData) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/reports');

      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final fileName = 'report_$timestamp.txt';
      final filePath = '${reportsDir.path}/$fileName';

      final file = File(filePath);
      final content = exportReportAsText(reportData);

      await file.writeAsString(content, encoding: systemEncoding);

      return filePath;
    } catch (e) {
      throw Exception('خطأ في حفظ التقرير: ${e.toString()}');
    }
  }

  /// الحصول على قائمة أنواع التقارير
  static List<Map<String, dynamic>> getReportTypes() {
    return [
      {
        'type': ReportType.comprehensive,
        'name': 'التقرير الشامل',
        'description': 'تقرير شامل لجميع المديونيات',
        'icon': 'assessment',
      },
      {
        'type': ReportType.byCustomer,
        'name': 'تقرير العملاء',
        'description': 'تقرير مديونيات العملاء',
        'icon': 'people',
      },
      {
        'type': ReportType.byEmployee,
        'name': 'تقرير الموظفين',
        'description': 'تقرير مديونيات الموظفين',
        'icon': 'badge',
      },
      {
        'type': ReportType.activeDebts,
        'name': 'المديونيات النشطة',
        'description': 'تقرير المديونيات غير المدفوعة',
        'icon': 'pending',
      },
      {
        'type': ReportType.paidDebts,
        'name': 'المديونيات المدفوعة',
        'description': 'تقرير المديونيات المدفوعة',
        'icon': 'check_circle',
      },
      {
        'type': ReportType.summary,
        'name': 'التقرير الملخص',
        'description': 'ملخص سريع للمديونيات',
        'icon': 'summarize',
      },
    ];
  }

  /// الحصول على قائمة فترات التقرير
  static List<Map<String, dynamic>> getReportPeriods() {
    return [
      {
        'period': ReportPeriod.today,
        'name': 'اليوم',
        'description': 'مديونيات اليوم',
      },
      {
        'period': ReportPeriod.thisWeek,
        'name': 'هذا الأسبوع',
        'description': 'مديونيات هذا الأسبوع',
      },
      {
        'period': ReportPeriod.thisMonth,
        'name': 'هذا الشهر',
        'description': 'مديونيات هذا الشهر',
      },
      {
        'period': ReportPeriod.lastMonth,
        'name': 'الشهر الماضي',
        'description': 'مديونيات الشهر الماضي',
      },
      {
        'period': ReportPeriod.thisYear,
        'name': 'هذا العام',
        'description': 'مديونيات هذا العام',
      },
      {
        'period': ReportPeriod.custom,
        'name': 'فترة مخصصة',
        'description': 'اختيار فترة زمنية محددة',
      },
      {
        'period': ReportPeriod.all,
        'name': 'جميع الفترات',
        'description': 'جميع المديونيات',
      },
    ];
  }
}
