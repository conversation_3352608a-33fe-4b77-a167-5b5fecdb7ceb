# دليل البدء السريع - ميزة الصور في تطبيق فقيه

## 🚀 البدء السريع

### 1. تشغيل التطبيق
```bash
cd c:\Users\<USER>\StudioProjects\faqeh
flutter run
```

### 2. اختبار ميزة الصور
1. افتح التطبيق
2. اضغط على "اختبار ميزة الصور"
3. جرب جميع الوظائف المتاحة

## 📱 الوظائف المتاحة

### التقاط صورة جديدة
- اضغط على "إضافة صورة"
- اختر "التقاط صورة"
- التقط الصورة واضغط ✓

### اختيار من المعرض
- اضغط على "إضافة صورة"
- اختر "اختيار من المعرض"
- حدد الصورة المطلوبة

### عرض الصورة
- اضغط على الصورة المصغرة
- استخدم الإيماءات للتكبير/التصغير
- اضغط ✕ للإغلاق

### حذف الصورة
- اضغط على أيقونة 🗑️ الحمراء
- أكد الحذف

## 🔧 حل المشاكل الشائعة

### مشكلة الأذونات
```
خطأ: "لا يمكن الوصول للكاميرا"
الحل: تأكد من منح الأذونات في إعدادات التطبيق
```

### مشكلة حجم الصورة
```
خطأ: "حجم الصورة كبير جداً"
الحل: سيتم ضغط الصورة تلقائياً، جرب مرة أخرى
```

### مشكلة تنسيق الصورة
```
خطأ: "تنسيق الصورة غير مدعوم"
الحل: استخدم صور بتنسيق JPG أو PNG فقط
```

## 🛠️ للمطورين

### إضافة ويدجت الصور لشاشة جديدة
```dart
import '../widgets/common/image_picker_widget.dart';

// في الشاشة
String? selectedImagePath;

ImagePickerWidget(
  imagePath: selectedImagePath,
  onImageChanged: (path) {
    setState(() {
      selectedImagePath = path;
    });
  },
)
```

### حفظ مديونية مع صورة
```dart
final debt = Debt(
  personId: customerId,
  personType: AppConstants.personTypeCustomer,
  amount: 1000.0,
  date: DateTime.now(),
  notes: "ملاحظة",
  imagePath: selectedImagePath, // مسار الصورة
);

await debtsService.addDebt(debt);
```

### عرض صورة مصغرة في قائمة
```dart
DebtImageThumbnail(
  imagePath: debt.imagePath,
  size: 50,
  showBorder: true,
)
```

## 📋 قائمة التحقق

### قبل التطوير
- [ ] تأكد من تثبيت جميع المكتبات
- [ ] تحقق من إعدادات Android SDK (35+)
- [ ] اختبر على جهاز حقيقي

### أثناء التطوير
- [ ] استخدم ImageService للعمليات
- [ ] تعامل مع الأخطاء بشكل صحيح
- [ ] اختبر الأذونات

### بعد التطوير
- [ ] اختبر جميع السيناريوهات
- [ ] تحقق من الأداء
- [ ] نظف الكود من التحذيرات

## 🎯 نصائح مهمة

### الأداء
- الصور يتم ضغطها تلقائياً
- احذف الصور غير المستخدمة دورياً
- استخدم الصور المصغرة في القوائم

### الأمان
- تحقق من الأذونات دائماً
- تعامل مع الأخطاء بوضوح
- لا تحفظ مسارات صور خارجية

### تجربة المستخدم
- أظهر رسائل واضحة للمستخدم
- استخدم مؤشرات التحميل
- وفر خيارات متعددة (كاميرا/معرض)

## 📞 الدعم

### في حالة وجود مشاكل:
1. تحقق من ملف `IMAGE_FEATURE_README.md`
2. راجع رسائل الخطأ في وحدة التحكم
3. اختبر على جهاز مختلف
4. تأكد من إعدادات الأذونات

### ملفات مهمة للمراجعة:
- `lib/services/image_service.dart`
- `lib/widgets/common/image_picker_widget.dart`
- `lib/models/debt.dart`
- `android/app/src/main/AndroidManifest.xml`

---

**تطبيق فقيه - إدارة المديونيات مع الصور** 📸
*دليل سريع للمطورين والمختبرين*
