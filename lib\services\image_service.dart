import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;
import '../constants/app_constants.dart';

/// خدمة إدارة الصور للمديونيات
class ImageService {
  static final ImageService _instance = ImageService._internal();
  static final ImagePicker _picker = ImagePicker();

  ImageService._internal();

  factory ImageService() => _instance;

  /// التقاط صورة من الكاميرا
  Future<String?> takePhoto() async {
    try {
      // طلب إذن الكاميرا
      final cameraPermission = await _requestCameraPermission();
      if (!cameraPermission) {
        throw Exception(AppConstants.errorCameraPermission);
      }

      // التقاط الصورة
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: AppConstants.imageQuality,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image == null) return null;

      // معالجة وحفظ الصورة
      return await _processAndSaveImage(image);
    } catch (e) {
      debugPrint('خطأ في التقاط الصورة: $e');
      rethrow;
    }
  }

  /// اختيار صورة من المعرض
  Future<String?> pickFromGallery() async {
    try {
      // طلب إذن المعرض
      final galleryPermission = await _requestGalleryPermission();
      if (!galleryPermission) {
        throw Exception(AppConstants.errorGalleryPermission);
      }

      // اختيار الصورة
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: AppConstants.imageQuality,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image == null) return null;

      // معالجة وحفظ الصورة
      return await _processAndSaveImage(image);
    } catch (e) {
      debugPrint('خطأ في اختيار الصورة: $e');
      rethrow;
    }
  }

  /// معالجة وحفظ الصورة
  Future<String> _processAndSaveImage(XFile image) async {
    try {
      // التحقق من حجم الصورة
      final file = File(image.path);
      final fileSizeKB = await file.length() / 1024;

      if (fileSizeKB > AppConstants.maxImageSizeKB * 10) {
        // 10 أضعاف الحد الأقصى للتحقق الأولي
        throw Exception(AppConstants.errorImageTooLarge);
      }

      // التحقق من تنسيق الصورة
      final extension = path.extension(image.path).toLowerCase().substring(1);
      if (!AppConstants.supportedImageFormats.contains(extension)) {
        throw Exception(AppConstants.errorUnsupportedFormat);
      }

      // ضغط الصورة
      final compressedImage = await _compressImage(image.path);
      if (compressedImage == null) {
        throw Exception(AppConstants.errorImageProcessing);
      }

      // حفظ الصورة في مجلد التطبيق
      final savedPath = await _saveImageToAppDirectory(compressedImage);

      // حذف الملف المؤقت
      await _deleteTemporaryFile(image.path);

      return savedPath;
    } catch (e) {
      debugPrint('خطأ في معالجة الصورة: $e');
      rethrow;
    }
  }

  /// ضغط الصورة
  Future<Uint8List?> _compressImage(String imagePath) async {
    try {
      final result = await FlutterImageCompress.compressWithFile(
        imagePath,
        quality: AppConstants.imageQuality,
        minWidth: 800,
        minHeight: 600,
        format: CompressFormat.jpeg,
      );

      // التحقق من حجم الصورة المضغوطة
      if (result != null &&
          result.length / 1024 > AppConstants.maxImageSizeKB) {
        // ضغط إضافي إذا كانت الصورة لا تزال كبيرة
        return await FlutterImageCompress.compressWithFile(
          imagePath,
          quality: 60,
          minWidth: 600,
          minHeight: 400,
          format: CompressFormat.jpeg,
        );
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في ضغط الصورة: $e');
      return null;
    }
  }

  /// حفظ الصورة في مجلد التطبيق
  Future<String> _saveImageToAppDirectory(Uint8List imageData) async {
    try {
      // الحصول على مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir =
          Directory(path.join(appDir.path, AppConstants.imagesFolder));

      // إنشاء المجلد إذا لم يكن موجوداً
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // إنشاء اسم ملف فريد
      final fileName = 'debt_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = path.join(imagesDir.path, fileName);

      // حفظ الصورة
      final file = File(filePath);
      await file.writeAsBytes(imageData);

      return filePath;
    } catch (e) {
      debugPrint('خطأ في حفظ الصورة: $e');
      rethrow;
    }
  }

  /// حذف صورة
  Future<bool> deleteImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف الصورة: $e');
      return false;
    }
  }

  /// التحقق من وجود الصورة
  Future<bool> imageExists(String imagePath) async {
    try {
      final file = File(imagePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// الحصول على حجم الصورة بالكيلوبايت
  Future<double?> getImageSizeKB(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        final sizeBytes = await file.length();
        return sizeBytes / 1024;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// حذف ملف مؤقت
  Future<void> _deleteTemporaryFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      debugPrint('خطأ في حذف الملف المؤقت: $e');
    }
  }

  /// طلب إذن الكاميرا
  Future<bool> _requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('خطأ في طلب إذن الكاميرا: $e');
      return false;
    }
  }

  /// طلب إذن المعرض
  Future<bool> _requestGalleryPermission() async {
    try {
      // للأندرويد 13 وما فوق، نحتاج إذن READ_MEDIA_IMAGES
      // للإصدارات الأقدم، نحتاج READ_EXTERNAL_STORAGE
      Permission permission = Permission.photos;

      // التحقق من إصدار الأندرويد
      if (await Permission.mediaLibrary.status.isDenied) {
        permission = Permission.mediaLibrary;
      }

      final status = await permission.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('خطأ في طلب إذن المعرض: $e');
      return false;
    }
  }

  /// التحقق من حالة أذونات الكاميرا
  Future<bool> isCameraPermissionGranted() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من حالة أذونات المعرض
  Future<bool> isGalleryPermissionGranted() async {
    try {
      // التحقق من الأذونات المختلفة حسب إصدار الأندرويد
      final photosStatus = await Permission.photos.status;
      final mediaLibraryStatus = await Permission.mediaLibrary.status;

      return photosStatus.isGranted || mediaLibraryStatus.isGranted;
    } catch (e) {
      return false;
    }
  }

  /// طلب جميع الأذونات المطلوبة للصور
  Future<Map<String, bool>> requestAllImagePermissions() async {
    try {
      final Map<Permission, PermissionStatus> statuses = await [
        Permission.camera,
        Permission.photos,
        Permission.mediaLibrary,
      ].request();

      return {
        'camera': statuses[Permission.camera]?.isGranted ?? false,
        'photos': statuses[Permission.photos]?.isGranted ?? false,
        'mediaLibrary': statuses[Permission.mediaLibrary]?.isGranted ?? false,
      };
    } catch (e) {
      debugPrint('خطأ في طلب الأذونات: $e');
      return {
        'camera': false,
        'photos': false,
        'mediaLibrary': false,
      };
    }
  }

  /// التحقق من جميع الأذونات المطلوبة
  Future<Map<String, bool>> checkAllImagePermissions() async {
    try {
      return {
        'camera': await isCameraPermissionGranted(),
        'gallery': await isGalleryPermissionGranted(),
      };
    } catch (e) {
      return {
        'camera': false,
        'gallery': false,
      };
    }
  }

  /// تنظيف الصور القديمة (حذف الصور غير المستخدمة)
  Future<void> cleanupUnusedImages(List<String> usedImagePaths) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir =
          Directory(path.join(appDir.path, AppConstants.imagesFolder));

      if (!await imagesDir.exists()) return;

      final files = await imagesDir.list().toList();
      for (final file in files) {
        if (file is File) {
          final filePath = file.path;
          if (!usedImagePaths.contains(filePath)) {
            await file.delete();
            debugPrint('تم حذف الصورة غير المستخدمة: $filePath');
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف الصور: $e');
    }
  }

  /// الحصول على معلومات الصورة
  Future<Map<String, dynamic>?> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) return null;

      final stat = await file.stat();
      final sizeKB = stat.size / 1024;
      final extension = path.extension(imagePath).toLowerCase().substring(1);

      return {
        'path': imagePath,
        'size_kb': sizeKB,
        'extension': extension,
        'created': stat.changed,
        'exists': true,
      };
    } catch (e) {
      return null;
    }
  }
}
