# ميزة إرفاق الصور للمديونيات - تطبيق فقيه

## نظرة عامة

تم إضافة ميزة شاملة لإرفاق الصور مع المديونيات في تطبيق فقيه، مما يتيح للمستخدمين:
- التقاط صور جديدة باستخدام الكاميرا
- اختيار صور من معرض الجهاز
- عرض الصور المرفقة مع المديونيات
- حذف الصور غير المرغوب فيها

## الوظائف المتاحة

### 📸 التقاط الصور
- فتح الكاميرا مباشرة من التطبيق
- ضغط تلقائي للصور لتوفير مساحة التخزين
- جودة محسنة (80%) مع حجم أقصى 500 KB

### 🖼️ اختيار من المعرض
- تصفح صور الجهاز
- اختيار صورة موجودة
- معاينة قبل التأكيد

### 👁️ عرض الصور
- عرض مصغر في قوائم المديونيات
- إمكانية التكبير والتصغير
- عرض بالحجم الكامل في حوار منفصل

### 🗑️ إدارة الصور
- حذف الصور المرفقة
- تنظيف الصور غير المستخدمة
- إدارة مساحة التخزين

## التقنيات المستخدمة

### المكتبات الأساسية
```yaml
dependencies:
  image_picker: ^1.1.2          # التقاط واختيار الصور
  permission_handler: ^12.0.0   # إدارة الأذونات
  flutter_image_compress: ^2.4.0 # ضغط الصور
  path_provider: ^2.1.5         # الوصول لمجلدات النظام
```

### قاعدة البيانات
- إضافة عمود `image_path` في جدول `debts`
- دعم مسارات الصور في جميع العمليات
- فهرسة محسنة للبحث

### الأذونات المطلوبة (Android)
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
```

## هيكل الملفات

### الخدمات
- `lib/services/image_service.dart` - خدمة إدارة الصور الرئيسية
- `lib/services/database/` - تحديث خدمات قاعدة البيانات

### النماذج
- `lib/models/debt.dart` - تحديث نموذج المديونية لدعم الصور

### الواجهات
- `lib/widgets/common/image_picker_widget.dart` - ويدجت اختيار الصور
- `lib/widgets/common/debt_image_thumbnail.dart` - عرض الصور المصغرة

### الاختبار
- `lib/views/test_image_screen.dart` - شاشة اختبار شاملة

## كيفية الاستخدام

### 1. اختبار الميزة
```bash
flutter run
```
ثم اضغط على "اختبار ميزة الصور" في الشاشة الرئيسية.

### 2. استخدام ويدجت اختيار الصور
```dart
ImagePickerWidget(
  imagePath: currentImagePath,
  onImageChanged: (newPath) {
    setState(() {
      currentImagePath = newPath;
    });
  },
  height: 200,
)
```

### 3. عرض صورة مصغرة
```dart
DebtImageThumbnail(
  imagePath: debt.imagePath,
  size: 60,
  onTap: () => showFullImage(debt.imagePath),
)
```

### 4. استخدام خدمة الصور
```dart
final imageService = ImageService();

// التقاط صورة
final imagePath = await imageService.takePhoto();

// اختيار من المعرض
final imagePath = await imageService.pickFromGallery();

// حذف صورة
await imageService.deleteImage(imagePath);
```

## الإعدادات القابلة للتخصيص

في `lib/constants/app_constants.dart`:

```dart
// إعدادات الصور
static const String imagesFolder = 'debt_images';
static const int maxImageSizeKB = 500;
static const int imageQuality = 80;
static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png'];
```

## معالجة الأخطاء

### الأخطاء الشائعة ورسائلها
- `errorCameraPermission`: "لا يمكن الوصول للكاميرا"
- `errorGalleryPermission`: "لا يمكن الوصول للمعرض"
- `errorImageTooLarge`: "حجم الصورة كبير جداً"
- `errorUnsupportedFormat`: "تنسيق الصورة غير مدعوم"
- `errorImageProcessing`: "خطأ في معالجة الصورة"

### التعامل مع الأخطاء
```dart
try {
  final imagePath = await imageService.takePhoto();
  // معالجة النجاح
} catch (e) {
  // عرض رسالة خطأ للمستخدم
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text(e.toString())),
  );
}
```

## الأداء والتحسين

### ضغط الصور
- ضغط تلقائي إلى 80% جودة
- حد أقصى 500 KB لكل صورة
- تحويل تلقائي إلى JPEG

### إدارة الذاكرة
- حذف الملفات المؤقتة تلقائياً
- تنظيف الصور غير المستخدمة
- تحميل كسول للصور

### الأمان
- التحقق من الأذونات قبل الوصول
- التحقق من صحة تنسيقات الصور
- حماية من الملفات الضارة

## الاختبار والتطوير

### تشغيل الاختبارات
```bash
flutter test
```

### فحص الكود
```bash
flutter analyze
```

### اختبار الأذونات
استخدم شاشة الاختبار المدمجة للتحقق من:
- حالة أذونات الكاميرا والمعرض
- وظائف الضغط والحفظ
- عرض معلومات الصور

## المتطلبات النظام

### Android
- Android SDK 35+
- مساحة تخزين كافية للصور
- كاميرا (للالتقاط)

### iOS
- iOS 12.0+
- أذونات الكاميرا والصور
- مساحة تخزين كافية

## الدعم والصيانة

### تحديث المكتبات
```bash
flutter pub upgrade
```

### تنظيف المشروع
```bash
flutter clean
flutter pub get
```

### حل مشاكل الأذونات
1. تأكد من إضافة الأذونات في AndroidManifest.xml
2. اختبر على جهاز حقيقي (ليس محاكي)
3. تحقق من إعدادات التطبيق في النظام

---

**تم تطوير هذه الميزة كجزء من تطبيق فقيه لإدارة المديونيات**
*آخر تحديث: 2025-06-27*
