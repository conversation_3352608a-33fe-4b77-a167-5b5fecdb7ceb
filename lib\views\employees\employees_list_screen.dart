import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../models/employee.dart';
import '../../services/database/employees_service.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/advanced_search_widget.dart';
import '../../widgets/employees/employee_card.dart';
import 'add_employee_screen.dart';
import 'edit_employee_screen.dart';
import 'employee_details_screen.dart';

/// شاشة قائمة الموظفين مع البحث والفرز المتقدم
class EmployeesListScreen extends StatefulWidget {
  const EmployeesListScreen({super.key});

  @override
  State<EmployeesListScreen> createState() => _EmployeesListScreenState();
}

class _EmployeesListScreenState extends State<EmployeesListScreen> {
  final EmployeesService _employeesService = EmployeesService();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<Employee> _allEmployees = [];
  List<Employee> _filteredEmployees = [];
  List<Map<String, dynamic>> _employeesWithDebts = [];

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // متغيرات البحث والفرز
  String _searchQuery = '';
  String _currentSortBy = 'name';
  bool _isAscending = true;

  // إحصائيات البحث
  int get _totalEmployees => _allEmployees.length;
  int get _filteredCount => _filteredEmployees.length;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الموظفين
  Future<void> _loadEmployees() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // تحميل الموظفين مع معلومات المديونيات
      final employeesWithDebts = await _employeesService.getEmployeesWithDebts(
        orderBy: _getSortOrderBy(),
      );

      final employees = await _employeesService.getSortedEmployees(
        sortBy: _currentSortBy,
        ascending: _isAscending,
      );

      setState(() {
        _allEmployees = employees;
        _employeesWithDebts = employeesWithDebts;
        _applySearchFilter();
        _isLoading = false;
      });

      _refreshController.refreshCompleted();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الموظفين: ${e.toString()}';
      });
      _refreshController.refreshFailed();
    }
  }

  /// تطبيق فلتر البحث
  void _applySearchFilter() {
    if (_searchQuery.isEmpty) {
      _filteredEmployees = List.from(_allEmployees);
    } else {
      _filteredEmployees = _allEmployees.where((employee) {
        final query = _searchQuery.toLowerCase();
        return employee.name.toLowerCase().contains(query) ||
            (employee.phone?.toLowerCase().contains(query) ?? false) ||
            (employee.email?.toLowerCase().contains(query) ?? false) ||
            (employee.position?.toLowerCase().contains(query) ?? false) ||
            (employee.department?.toLowerCase().contains(query) ?? false);
      }).toList();
    }
  }

  /// الحصول على ترتيب الفرز
  String _getSortOrderBy() {
    final direction = _isAscending ? 'ASC' : 'DESC';
    switch (_currentSortBy) {
      case 'name':
        return 'e.name $direction';
      case 'phone':
        return 'e.phone $direction';
      case 'email':
        return 'e.email $direction';
      case 'position':
        return 'e.position $direction';
      case 'department':
        return 'e.department $direction';
      case 'created_at':
        return 'e.created_at $direction';
      default:
        return 'e.name ASC';
    }
  }

  /// البحث في الموظفين
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _applySearchFilter();
    });
  }

  /// تغيير معايير الفرز
  void _onSortChanged(String sortBy, bool ascending) {
    setState(() {
      _currentSortBy = sortBy;
      _isAscending = ascending;
    });
    _loadEmployees();
  }

  /// الحصول على معلومات المديونية للموظف
  Map<String, dynamic>? _getEmployeeDebtInfo(int employeeId) {
    try {
      return _employeesWithDebts.firstWhere(
        (emp) => emp['id'] == employeeId,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('الموظفين'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // عرض عدد النتائج
        if (!_isLoading)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                _searchQuery.isEmpty
                    ? '$_totalEmployees موظف'
                    : '$_filteredCount من $_totalEmployees',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return Column(
      children: [
        // ويدجت البحث والفرز
        _buildSearchAndSort(),

        // المحتوى الرئيسي
        Expanded(
          child: _buildMainContent(),
        ),
      ],
    );
  }

  /// بناء ويدجت البحث والفرز
  Widget _buildSearchAndSort() {
    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // حقل البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: AdvancedSearchWidget(
              hintText: 'البحث في الموظفين...',
              onSearchChanged: _onSearchChanged,
              initialQuery: _searchQuery,
            ),
          ),

          // خيارات الفرز
          if (_totalEmployees > 0)
            SortOptionsWidget(
              currentSortBy: _currentSortBy,
              ascending: _isAscending,
              onSortChanged: _onSortChanged,
              sortOptions: const [
                SortOption(value: 'name', label: 'الاسم'),
                SortOption(value: 'position', label: 'المنصب'),
                SortOption(value: 'department', label: 'القسم'),
                SortOption(value: 'phone', label: 'الهاتف'),
                SortOption(value: 'email', label: 'البريد الإلكتروني'),
                SortOption(value: 'created_at', label: 'تاريخ الإضافة'),
              ],
            ),
        ],
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_hasError) {
      return _buildErrorState();
    }

    if (_totalEmployees == 0) {
      return _buildEmptyState();
    }

    if (_filteredEmployees.isEmpty) {
      return _buildNoResultsState();
    }

    return _buildEmployeesList();
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل الموظفين...',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadEmployees,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة عدم وجود موظفين
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'لا يوجد موظفين',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ابدأ بإضافة موظف جديد لإدارة بياناتهم',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _navigateToAddEmployee,
              icon: const Icon(Icons.add),
              label: const Text('إضافة موظف جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة عدم وجود نتائج بحث
  Widget _buildNoResultsState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على موظفين يطابقون البحث "$_searchQuery"',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _searchQuery = '';
                  _applySearchFilter();
                });
              },
              icon: const Icon(Icons.clear),
              label: const Text('مسح البحث'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الموظفين
  Widget _buildEmployeesList() {
    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _loadEmployees,
      header: const WaterDropMaterialHeader(
        backgroundColor: AppTheme.primaryColor,
        color: Colors.white,
      ),
      child: AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredEmployees.length,
          itemBuilder: (context, index) {
            final employee = _filteredEmployees[index];
            final debtInfo = _getEmployeeDebtInfo(employee.id!);

            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: EmployeeCard(
                      employee: employee,
                      totalDebt:
                          debtInfo?['total_active_debt']?.toDouble() ?? 0.0,
                      debtCount: debtInfo?['debt_count'] ?? 0,
                      onTap: () => _navigateToEmployeeDetails(employee),
                      onEdit: () => _navigateToEditEmployee(employee),
                      onDelete: () => _showDeleteConfirmation(employee),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToAddEmployee,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('إضافة موظف'),
    );
  }

  /// التنقل لإضافة موظف جديد
  void _navigateToAddEmployee() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEmployeeScreen(),
      ),
    );

    if (result == true) {
      _loadEmployees();
    }
  }

  /// التنقل لتعديل موظف
  void _navigateToEditEmployee(Employee employee) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditEmployeeScreen(employee: employee),
      ),
    );

    if (result == true) {
      _loadEmployees();
    }
  }

  /// التنقل لتفاصيل الموظف
  void _navigateToEmployeeDetails(Employee employee) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EmployeeDetailsScreen(employee: employee),
      ),
    );

    if (result == true) {
      _loadEmployees();
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Employee employee) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف الموظف "${employee.name}"؟\n\n'
            'سيتم حذف جميع البيانات المرتبطة بهذا الموظف نهائياً.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteEmployee(employee);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// حذف موظف
  void _deleteEmployee(Employee employee) async {
    try {
      await _employeesService.deleteEmployee(employee.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الموظف "${employee.name}" بنجاح'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'تراجع',
              textColor: Colors.white,
              onPressed: () {
                // يمكن إضافة وظيفة التراجع هنا لاحقاً
              },
            ),
          ),
        );

        _loadEmployees();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الموظف: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
