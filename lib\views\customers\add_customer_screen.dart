import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/customer.dart';
import '../../services/database/customers_service.dart';
import '../../widgets/common/form_field_widget.dart';

/// شاشة إضافة عميل جديد
class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final CustomersService _customersService = CustomersService();

  // متحكمات النصوص
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// حفظ العميل الجديد
  Future<void> _saveCustomer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء العميل الجديد
      final customer = Customer(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isNotEmpty
            ? _phoneController.text.trim()
            : null,
        email: _emailController.text.trim().isNotEmpty
            ? _emailController.text.trim()
            : null,
        address: _addressController.text.trim().isNotEmpty
            ? _addressController.text.trim()
            : null,
      );

      // التحقق من صحة البيانات
      final validationErrors = customer.validate();
      if (validationErrors.isNotEmpty) {
        _showErrorDialog('خطأ في البيانات', validationErrors.join('\n'));
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // التحقق من عدم تكرار الاسم
      final nameExists =
          await _customersService.isCustomerNameExists(customer.name);
      if (nameExists) {
        _showErrorDialog('خطأ', 'يوجد عميل بنفس الاسم مسبقاً');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // التحقق من عدم تكرار رقم الهاتف
      if (customer.phone != null && customer.phone!.isNotEmpty) {
        final phoneExists =
            await _customersService.isCustomerPhoneExists(customer.phone!);
        if (phoneExists) {
          _showErrorDialog('خطأ', 'يوجد عميل بنفس رقم الهاتف مسبقاً');
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      // التحقق من عدم تكرار البريد الإلكتروني
      if (customer.email != null && customer.email!.isNotEmpty) {
        final emailExists =
            await _customersService.isCustomerEmailExists(customer.email!);
        if (emailExists) {
          _showErrorDialog('خطأ', 'يوجد عميل بنفس البريد الإلكتروني مسبقاً');
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      // حفظ العميل
      await _customersService.addCustomer(customer);

      // التحقق من أن الويدجت لا يزال مُحمل
      if (!mounted) return;

      // عرض رسالة نجاح والعودة
      _showSuccessSnackBar('تم إضافة العميل بنجاح');
      Navigator.pop(context, true);
    } catch (e) {
      _showErrorDialog('خطأ', 'حدث خطأ أثناء حفظ العميل: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// التحقق من صحة الاسم
  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.errorRequired;
    }
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون أكثر من حرف واحد';
    }
    if (value.trim().length > 100) {
      return 'الاسم يجب أن يكون أقل من 100 حرف';
    }
    return null;
  }

  /// التحقق من صحة رقم الهاتف
  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final phoneRegex = RegExp(r'^[0-9+\-\s()]+$');
    if (!phoneRegex.hasMatch(value)) {
      return AppConstants.errorInvalidPhone;
    }

    if (value.length < 7 || value.length > 20) {
      return 'رقم الهاتف يجب أن يكون بين 7 و 20 رقم';
    }

    return null;
  }

  /// التحقق من صحة البريد الإلكتروني
  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return AppConstants.errorInvalidEmail;
    }

    return null;
  }

  /// التحقق من صحة العنوان
  String? _validateAddress(String? value) {
    if (value != null && value.length > 500) {
      return 'العنوان يجب أن يكون أقل من 500 حرف';
    }
    return null;
  }

  /// عرض رسالة خطأ
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة عميل جديد'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveCustomer,
              child: const Text(
                AppConstants.save,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رسالة ترحيبية
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.person_add,
                          color: AppTheme.primaryColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'إضافة عميل جديد',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'املأ البيانات التالية لإضافة عميل جديد. الحقول المطلوبة مميزة بعلامة *',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // حقل الاسم (مطلوب)
              FormFieldWidget(
                label: '${AppConstants.name} *',
                controller: _nameController,
                validator: _validateName,
                keyboardType: TextInputType.name,
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.person,
                enabled: !_isLoading,
              ),

              const SizedBox(height: 16),

              // حقل رقم الهاتف (اختياري)
              FormFieldWidget(
                label: AppConstants.phone,
                controller: _phoneController,
                validator: _validatePhone,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.phone,
                enabled: !_isLoading,
              ),

              const SizedBox(height: 16),

              // حقل البريد الإلكتروني (اختياري)
              FormFieldWidget(
                label: AppConstants.email,
                controller: _emailController,
                validator: _validateEmail,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.email,
                enabled: !_isLoading,
              ),

              const SizedBox(height: 16),

              // حقل العنوان (اختياري)
              FormFieldWidget(
                label: AppConstants.address,
                controller: _addressController,
                validator: _validateAddress,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.done,
                prefixIcon: Icons.location_on,
                maxLines: 3,
                enabled: !_isLoading,
              ),

              const SizedBox(height: 32),

              // زر الحفظ
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _saveCustomer,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.save),
                  label: Text(_isLoading ? 'جاري الحفظ...' : AppConstants.save),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // ملاحظة
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'يمكنك إضافة معلومات الاتصال لاحقاً من خلال تعديل بيانات العميل',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
