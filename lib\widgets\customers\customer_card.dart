import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/customer.dart';

/// بطاقة عرض العميل
class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;
  final Map<String, dynamic>? debtInfo;

  const CustomerCard({
    super.key,
    required this.customer,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
    this.debtInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: showActions
          ? Slidable(
              key: ValueKey(customer.id),
              endActionPane: ActionPane(
                motion: const ScrollMotion(),
                children: [
                  if (onEdit != null)
                    SlidableAction(
                      onPressed: (_) => onEdit!(),
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      icon: Icons.edit,
                      label: AppConstants.edit,
                    ),
                  if (onDelete != null)
                    SlidableAction(
                      onPressed: (_) => onDelete!(),
                      backgroundColor: AppTheme.errorColor,
                      foregroundColor: Colors.white,
                      icon: Icons.delete,
                      label: AppConstants.delete,
                    ),
                ],
              ),
              child: _buildCard(context),
            )
          : _buildCard(context),
    );
  }

  Widget _buildCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: الاسم والأيقونة
              Row(
                children: [
                  // أيقونة العميل
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Center(
                      child: Text(
                        customer.initials,
                        style: const TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // اسم العميل ومعلومات إضافية
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          customer.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        if (customer.phone != null &&
                            customer.phone!.isNotEmpty)
                          Row(
                            children: [
                              const Icon(
                                Icons.phone,
                                size: 14,
                                color: AppTheme.textSecondaryColor,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  customer.phone!,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),

                  // معلومات المديونية (إذا توفرت)
                  if (debtInfo != null) _buildDebtInfo(),
                ],
              ),

              // الصف الثاني: معلومات إضافية
              if (customer.email != null && customer.email!.isNotEmpty ||
                  customer.address != null && customer.address!.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildAdditionalInfo(),
              ],

              // الصف الثالث: تاريخ الإضافة
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.access_time,
                        size: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'أضيف في ${customer.formattedCreatedAt}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),

                  // مؤشر الحالة
                  _buildStatusIndicator(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDebtInfo() {
    final totalDebtValue = debtInfo!['total_active_debt'];
    final totalDebt = totalDebtValue is int
        ? totalDebtValue.toDouble()
        : (totalDebtValue as double? ?? 0.0);
    final debtCount = debtInfo!['debt_count'] as int? ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (totalDebt > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${totalDebt.toStringAsFixed(0)} ريال',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.errorColor,
              ),
            ),
          ),
          const SizedBox(height: 4),
        ],
        if (debtCount > 0)
          Text(
            '$debtCount ${debtCount == 1 ? 'مديونية' : 'مديونيات'}',
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      children: [
        if (customer.email != null && customer.email!.isNotEmpty)
          Row(
            children: [
              const Icon(
                Icons.email,
                size: 14,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  customer.email!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        if (customer.address != null && customer.address!.isNotEmpty) ...[
          if (customer.email != null && customer.email!.isNotEmpty)
            const SizedBox(height: 4),
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 14,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  customer.address!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildStatusIndicator() {
    final hasCompleteInfo = customer.isComplete;

    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: hasCompleteInfo ? AppTheme.successColor : AppTheme.warningColor,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

/// بطاقة عميل مبسطة للاختيار
class CustomerSelectionCard extends StatelessWidget {
  final Customer customer;
  final bool isSelected;
  final VoidCallback? onTap;

  const CustomerSelectionCard({
    super.key,
    required this.customer,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Card(
        elevation: isSelected ? 4 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected ? AppTheme.primaryColor : Colors.transparent,
            width: 2,
          ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // أيقونة العميل
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      customer.initials,
                      style: TextStyle(
                        color:
                            isSelected ? Colors.white : AppTheme.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // معلومات العميل
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customer.name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? AppTheme.primaryColor
                              : AppTheme.textPrimaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (customer.phone != null &&
                          customer.phone!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          customer.phone!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),

                // أيقونة الاختيار
                if (isSelected)
                  const Icon(
                    Icons.check_circle,
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
