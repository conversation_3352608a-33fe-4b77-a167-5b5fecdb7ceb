import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../constants/app_constants.dart';

/// مساعد قاعدة البيانات الرئيسي
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// إنشاء الجداول عند إنشاء قاعدة البيانات لأول مرة
  Future<void> _onCreate(Database db, int version) async {
    await _createCustomersTable(db);
    await _createEmployeesTable(db);
    await _createDebtsTable(db);
  }

  /// تحديث قاعدة البيانات عند تغيير الإصدار
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // سيتم إضافة منطق التحديث هنا عند الحاجة
  }

  /// إنشاء جدول العملاء
  Future<void> _createCustomersTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${AppConstants.customersTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    ''');
  }

  /// إنشاء جدول الموظفين
  Future<void> _createEmployeesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${AppConstants.employeesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        position TEXT,
        department TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    ''');
  }

  /// إنشاء جدول المديونيات
  Future<void> _createDebtsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${AppConstants.debtsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        person_id INTEGER NOT NULL,
        person_type TEXT NOT NULL CHECK (person_type IN ('${AppConstants.personTypeCustomer}', '${AppConstants.personTypeEmployee}')),
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        notes TEXT,
        image_path TEXT,
        status TEXT NOT NULL DEFAULT '${AppConstants.debtStatusActive}' CHECK (status IN ('${AppConstants.debtStatusActive}', '${AppConstants.debtStatusPaid}', '${AppConstants.debtStatusCancelled}')),
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (person_id) REFERENCES ${AppConstants.customersTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (person_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
      )
    ''');
  }

  /// إدراج سجل جديد
  Future<int> insert(String table, Map<String, dynamic> values) async {
    final db = await database;
    values['updated_at'] = DateTime.now().toIso8601String();
    return await db.insert(table, values);
  }

  /// تحديث سجل موجود
  Future<int> update(String table, Map<String, dynamic> values,
      String whereClause, List<dynamic> whereArgs) async {
    final db = await database;
    values['updated_at'] = DateTime.now().toIso8601String();
    return await db.update(table, values,
        where: whereClause, whereArgs: whereArgs);
  }

  /// حذف سجل
  Future<int> delete(
      String table, String whereClause, List<dynamic> whereArgs) async {
    final db = await database;
    return await db.delete(table, where: whereClause, whereArgs: whereArgs);
  }

  /// استعلام البيانات
  Future<List<Map<String, dynamic>>> query(
    String table, {
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  /// استعلام خام
  Future<List<Map<String, dynamic>>> rawQuery(String sql,
      [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  /// تنفيذ استعلام خام
  Future<void> rawExecute(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    await db.rawQuery(sql, arguments);
  }

  /// البحث في جدول معين
  Future<List<Map<String, dynamic>>> search(
    String table,
    String searchTerm, {
    List<String>? searchColumns,
    String? additionalWhere,
    List<dynamic>? additionalWhereArgs,
    String? orderBy,
    int? limit,
  }) async {
    if (searchColumns == null || searchColumns.isEmpty) {
      return [];
    }

    final db = await database;

    // بناء شرط البحث
    final searchConditions =
        searchColumns.map((column) => '$column LIKE ?').join(' OR ');
    final searchArgs = searchColumns.map((column) => '%$searchTerm%').toList();

    String whereClause = '($searchConditions)';
    List<dynamic> whereArgs = searchArgs;

    // إضافة شروط إضافية إذا وجدت
    if (additionalWhere != null && additionalWhere.isNotEmpty) {
      whereClause += ' AND $additionalWhere';
      if (additionalWhereArgs != null) {
        whereArgs.addAll(additionalWhereArgs);
      }
    }

    return await db.query(
      table,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
    );
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  /// حذف قاعدة البيانات (للاختبار فقط)
  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
