import 'package:intl/intl.dart';

/// نموذج بيانات العميل
class Customer {
  final int? id;
  final String name;
  final String? phone;
  final String? email;
  final String? address;
  final DateTime createdAt;
  final DateTime updatedAt;

  Customer({
    this.id,
    required this.name,
    this.phone,
    this.email,
    this.address,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء عميل من Map (من قاعدة البيانات)
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'] as int?,
      name: map['name'] as String,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// تحويل العميل إلى Map (لقاعدة البيانات)
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من العميل
  Customer copyWith({
    int? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// التحقق من صحة البيانات
  List<String> validate() {
    List<String> errors = [];

    // التحقق من الاسم
    if (name.trim().isEmpty) {
      errors.add('الاسم مطلوب');
    } else if (name.trim().length < 2) {
      errors.add('الاسم يجب أن يكون أكثر من حرف واحد');
    } else if (name.trim().length > 100) {
      errors.add('الاسم يجب أن يكون أقل من 100 حرف');
    }

    // التحقق من رقم الهاتف
    if (phone != null && phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^[0-9+\-\s()]+$');
      if (!phoneRegex.hasMatch(phone!)) {
        errors.add('رقم الهاتف غير صحيح');
      } else if (phone!.length < 7 || phone!.length > 20) {
        errors.add('رقم الهاتف يجب أن يكون بين 7 و 20 رقم');
      }
    }

    // التحقق من البريد الإلكتروني
    if (email != null && email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegex.hasMatch(email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    // التحقق من العنوان
    if (address != null && address!.isNotEmpty && address!.length > 500) {
      errors.add('العنوان يجب أن يكون أقل من 500 حرف');
    }

    return errors;
  }

  /// التحقق من صحة البيانات (إرجاع true إذا كانت صحيحة)
  bool get isValid => validate().isEmpty;

  /// الحصول على الاسم المختصر (الحروف الأولى)
  String get initials {
    final words = name.trim().split(' ');
    if (words.isEmpty) return '';
    
    if (words.length == 1) {
      return words[0].isNotEmpty ? words[0][0].toUpperCase() : '';
    }
    
    return words.take(2)
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
        .join('');
  }

  /// الحصول على معلومات الاتصال المتاحة
  List<String> get contactInfo {
    List<String> contacts = [];
    if (phone != null && phone!.isNotEmpty) {
      contacts.add('هاتف: $phone');
    }
    if (email != null && email!.isNotEmpty) {
      contacts.add('إيميل: $email');
    }
    return contacts;
  }

  /// تنسيق تاريخ الإنشاء للعرض
  String get formattedCreatedAt {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(createdAt);
  }

  /// تنسيق تاريخ التحديث للعرض
  String get formattedUpdatedAt {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(updatedAt);
  }

  /// الحصول على نص وصفي للعميل
  String get displayText {
    String text = name;
    if (phone != null && phone!.isNotEmpty) {
      text += ' - $phone';
    }
    return text;
  }

  /// التحقق من وجود معلومات اتصال
  bool get hasContactInfo {
    return (phone != null && phone!.isNotEmpty) || 
           (email != null && email!.isNotEmpty);
  }

  /// التحقق من اكتمال البيانات الأساسية
  bool get isComplete {
    return name.isNotEmpty && hasContactInfo;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer &&
        other.id == id &&
        other.name == name &&
        other.phone == phone &&
        other.email == email &&
        other.address == address;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, phone, email, address);
  }

  @override
  String toString() {
    return 'Customer{id: $id, name: $name, phone: $phone, email: $email, address: $address, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  /// تحويل إلى JSON للتصدير
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء عميل من JSON
  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] as int?,
      name: json['name'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      address: json['address'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
    );
  }
}
