import 'dart:io';
import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';

/// ويدجت عرض صورة مصغرة للمديونية
class DebtImageThumbnail extends StatelessWidget {
  final String? imagePath;
  final double size;
  final VoidCallback? onTap;
  final bool showBorder;

  const DebtImageThumbnail({
    super.key,
    this.imagePath,
    this.size = 50,
    this.onTap,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    if (imagePath == null || imagePath!.isEmpty) {
      return _buildPlaceholder();
    }

    return GestureDetector(
      onTap: onTap ?? () => _showFullImage(context),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: showBorder
              ? Border.all(
                  color: Colors.grey[300]!,
                  width: 1,
                )
              : null,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(showBorder ? 7 : 8),
          child: Image.file(
            File(imagePath!),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildErrorWidget();
            },
          ),
        ),
      ),
    );
  }

  /// عرض placeholder عندما لا توجد صورة
  Widget _buildPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: showBorder
            ? Border.all(
                color: Colors.grey[300]!,
                width: 1,
              )
            : null,
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey[400],
        size: size * 0.4,
      ),
    );
  }

  /// عرض widget الخطأ
  Widget _buildErrorWidget() {
    return Container(
      width: size,
      height: size,
      color: Colors.grey[200],
      child: Icon(
        Icons.broken_image,
        color: Colors.grey[400],
        size: size * 0.4,
      ),
    );
  }

  /// عرض الصورة بالحجم الكامل
  void _showFullImage(BuildContext context) {
    if (imagePath == null || imagePath!.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // الصورة
            Center(
              child: InteractiveViewer(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                  ),
                  child: Image.file(
                    File(imagePath!),
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 200,
                        height: 200,
                        color: Colors.grey[200],
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'خطأ في تحميل الصورة',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // زر الإغلاق
            Positioned(
              top: 40,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),

            // معلومات الصورة
            Positioned(
              bottom: 40,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    const Icon(
                      Icons.zoom_in,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'اسحب للتكبير والتصغير',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// ويدجت أيقونة الصورة للمديونية
class DebtImageIcon extends StatelessWidget {
  final bool hasImage;
  final double size;
  final VoidCallback? onTap;

  const DebtImageIcon({
    super.key,
    required this.hasImage,
    this.size = 24,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: hasImage
              ? AppTheme.successColor.withOpacity(0.1)
              : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          hasImage ? Icons.image : Icons.image_not_supported,
          size: size,
          color: hasImage ? AppTheme.successColor : Colors.grey,
        ),
      ),
    );
  }
}

/// ويدجت شارة الصورة
class ImageBadge extends StatelessWidget {
  final bool hasImage;
  final double size;

  const ImageBadge({
    super.key,
    required this.hasImage,
    this.size = 16,
  });

  @override
  Widget build(BuildContext context) {
    if (!hasImage) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: AppTheme.successColor,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Icon(
        Icons.image,
        size: size * 0.7,
        color: Colors.white,
      ),
    );
  }
}
