import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../models/employee.dart';
import '../../constants/app_theme.dart';

/// بطاقة عرض الموظف مع المعلومات الأساسية والمديونيات
class EmployeeCard extends StatelessWidget {
  final Employee employee;
  final double totalDebt;
  final int debtCount;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const EmployeeCard({
    super.key,
    required this.employee,
    this.totalDebt = 0.0,
    this.debtCount = 0,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: Value<PERSON>ey(employee.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          if (onEdit != null)
            SlidableAction(
              onPressed: (_) => onEdit!(),
              backgroundColor: AppTheme.secondaryColor,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: 'تعديل',
            ),
          if (onDelete != null)
            SlidableAction(
              onPressed: (_) => onDelete!(),
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              icon: Icons.delete,
              label: 'حذف',
            ),
        ],
      ),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف الأول: الاسم والحروف الأولى
                Row(
                  children: [
                    // الحروف الأولى
                    _buildAvatar(),
                    const SizedBox(width: 12),
                    
                    // معلومات الموظف الأساسية
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // الاسم
                          Text(
                            employee.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          
                          // المنصب والقسم
                          if (employee.hasWorkInfo)
                            const SizedBox(height: 4),
                          if (employee.position != null && employee.position!.isNotEmpty)
                            Text(
                              employee.position!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          if (employee.department != null && employee.department!.isNotEmpty)
                            Text(
                              employee.department!,
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                        ],
                      ),
                    ),
                    
                    // مؤشر المديونية
                    if (totalDebt > 0)
                      _buildDebtIndicator(),
                  ],
                ),
                
                // معلومات الاتصال
                if (employee.hasContactInfo) ...[
                  const SizedBox(height: 12),
                  _buildContactInfo(),
                ],
                
                // معلومات المديونية
                if (totalDebt > 0 || debtCount > 0) ...[
                  const SizedBox(height: 12),
                  _buildDebtInfo(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء الأفاتار مع الحروف الأولى
  Widget _buildAvatar() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          employee.initials,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
      ),
    );
  }

  /// بناء مؤشر المديونية
  Widget _buildDebtIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.account_balance_wallet,
            size: 16,
            color: Colors.orange[700],
          ),
          const SizedBox(width: 4),
          Text(
            '${totalDebt.toStringAsFixed(0)} ر.س',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الاتصال
  Widget _buildContactInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (employee.phone != null && employee.phone!.isNotEmpty)
          _buildInfoRow(
            icon: Icons.phone,
            text: employee.phone!,
            color: AppTheme.secondaryColor,
          ),
        if (employee.email != null && employee.email!.isNotEmpty) ...[
          if (employee.phone != null && employee.phone!.isNotEmpty)
            const SizedBox(height: 4),
          _buildInfoRow(
            icon: Icons.email,
            text: employee.email!,
            color: AppTheme.secondaryColor,
          ),
        ],
      ],
    );
  }

  /// بناء معلومات المديونية
  Widget _buildDebtInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: totalDebt > 0 ? Colors.orange.withOpacity(0.05) : Colors.green.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: totalDebt > 0 ? Colors.orange.withOpacity(0.2) : Colors.green.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            totalDebt > 0 ? Icons.trending_up : Icons.check_circle,
            size: 20,
            color: totalDebt > 0 ? Colors.orange[700] : Colors.green[700],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  totalDebt > 0 
                      ? 'إجمالي المديونية: ${totalDebt.toStringAsFixed(2)} ر.س'
                      : 'لا توجد مديونيات نشطة',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: totalDebt > 0 ? Colors.orange[700] : Colors.green[700],
                  ),
                ),
                if (debtCount > 0)
                  Text(
                    'عدد المديونيات: $debtCount',
                    style: TextStyle(
                      fontSize: 12,
                      color: totalDebt > 0 ? Colors.orange[600] : Colors.green[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 13,
              color: color,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

/// بطاقة مبسطة لاختيار الموظف
class EmployeeSelectionCard extends StatelessWidget {
  final Employee employee;
  final bool isSelected;
  final VoidCallback? onTap;

  const EmployeeSelectionCard({
    super.key,
    required this.employee,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // الحروف الأولى
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppTheme.primaryColor 
                      : AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    employee.initials,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // معلومات الموظف
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      employee.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimaryColor,
                      ),
                    ),
                    if (employee.position != null && employee.position!.isNotEmpty)
                      Text(
                        employee.position!,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                  ],
                ),
              ),
              
              // أيقونة الاختيار
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
