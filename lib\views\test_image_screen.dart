import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/app_theme.dart';
import '../widgets/common/image_picker_widget.dart';
import '../widgets/common/debt_image_thumbnail.dart';
import '../services/image_service.dart';

/// شاشة تجريبية لاختبار ميزة الصور
class TestImageScreen extends StatefulWidget {
  const TestImageScreen({super.key});

  @override
  State<TestImageScreen> createState() => _TestImageScreenState();
}

class _TestImageScreenState extends State<TestImageScreen> {
  String? _selectedImagePath;
  final ImageService _imageService = ImageService();
  List<String> _testImages = [];

  @override
  void initState() {
    super.initState();
    _loadTestImages();
  }

  Future<void> _loadTestImages() async {
    // يمكن إضافة صور تجريبية هنا
    setState(() {
      _testImages = [
        _selectedImagePath,
      ].where((path) => path != null && path.isNotEmpty).cast<String>().toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار ميزة الصور'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            const Text(
              'اختبار ويدجت اختيار الصور',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // ويدجت اختيار الصور
            ImagePickerWidget(
              imagePath: _selectedImagePath,
              onImageChanged: (imagePath) {
                setState(() {
                  _selectedImagePath = imagePath;
                });
                _loadTestImages();
              },
              height: 200,
            ),
            
            const SizedBox(height: 24),

            // معلومات الصورة المحددة
            if (_selectedImagePath != null) ...[
              const Text(
                'معلومات الصورة المحددة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              
              FutureBuilder<Map<String, dynamic>?>(
                future: _imageService.getImageInfo(_selectedImagePath!),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const CircularProgressIndicator();
                  }
                  
                  final info = snapshot.data;
                  if (info == null) {
                    return const Text('لا يمكن الحصول على معلومات الصورة');
                  }
                  
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoRow('المسار:', info['path']),
                          _buildInfoRow('الحجم:', '${info['size_kb'].toStringAsFixed(2)} KB'),
                          _buildInfoRow('التنسيق:', info['extension']),
                          _buildInfoRow('تاريخ الإنشاء:', info['created'].toString()),
                          _buildInfoRow('موجود:', info['exists'] ? 'نعم' : 'لا'),
                        ],
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 24),
            ],

            // اختبار الصور المصغرة
            const Text(
              'اختبار الصور المصغرة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                // صورة مصغرة صغيرة
                Column(
                  children: [
                    DebtImageThumbnail(
                      imagePath: _selectedImagePath,
                      size: 40,
                    ),
                    const SizedBox(height: 4),
                    const Text('40x40', style: TextStyle(fontSize: 10)),
                  ],
                ),
                
                const SizedBox(width: 16),
                
                // صورة مصغرة متوسطة
                Column(
                  children: [
                    DebtImageThumbnail(
                      imagePath: _selectedImagePath,
                      size: 60,
                    ),
                    const SizedBox(height: 4),
                    const Text('60x60', style: TextStyle(fontSize: 10)),
                  ],
                ),
                
                const SizedBox(width: 16),
                
                // صورة مصغرة كبيرة
                Column(
                  children: [
                    DebtImageThumbnail(
                      imagePath: _selectedImagePath,
                      size: 80,
                    ),
                    const SizedBox(height: 4),
                    const Text('80x80', style: TextStyle(fontSize: 10)),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 24),

            // اختبار أيقونات الصور
            const Text(
              'اختبار أيقونات الصور',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                // أيقونة مع صورة
                Column(
                  children: [
                    DebtImageIcon(
                      hasImage: _selectedImagePath != null,
                      size: 24,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _selectedImagePath != null ? 'مع صورة' : 'بدون صورة',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
                
                const SizedBox(width: 16),
                
                // شارة الصورة
                Column(
                  children: [
                    ImageBadge(
                      hasImage: _selectedImagePath != null,
                      size: 20,
                    ),
                    const SizedBox(height: 4),
                    const Text('شارة', style: TextStyle(fontSize: 10)),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 24),

            // أزرار الاختبار
            const Text(
              'اختبارات إضافية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _checkPermissions,
                  icon: const Icon(Icons.security),
                  label: const Text('فحص الأذونات'),
                ),
                
                ElevatedButton.icon(
                  onPressed: _selectedImagePath != null ? _deleteCurrentImage : null,
                  icon: const Icon(Icons.delete),
                  label: const Text('حذف الصورة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: _cleanupImages,
                  icon: const Icon(Icons.cleaning_services),
                  label: const Text('تنظيف الصور'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),

            // معلومات الإعدادات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات الصور',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow('الحد الأقصى للحجم:', '${AppConstants.maxImageSizeKB} KB'),
                    _buildInfoRow('جودة الضغط:', '${AppConstants.imageQuality}%'),
                    _buildInfoRow('التنسيقات المدعومة:', AppConstants.supportedImageFormats.join(', ')),
                    _buildInfoRow('مجلد الصور:', AppConstants.imagesFolder),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _checkPermissions() async {
    final cameraPermission = await _imageService.isCameraPermissionGranted();
    final galleryPermission = await _imageService.isGalleryPermissionGranted();
    
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة الأذونات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildPermissionRow('الكاميرا', cameraPermission),
            _buildPermissionRow('المعرض', galleryPermission),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionRow(String name, bool granted) {
    return Row(
      children: [
        Icon(
          granted ? Icons.check_circle : Icons.cancel,
          color: granted ? Colors.green : Colors.red,
        ),
        const SizedBox(width: 8),
        Text('$name: ${granted ? 'مسموح' : 'غير مسموح'}'),
      ],
    );
  }

  Future<void> _deleteCurrentImage() async {
    if (_selectedImagePath == null) return;
    
    final success = await _imageService.deleteImage(_selectedImagePath!);
    setState(() {
      _selectedImagePath = null;
    });
    
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(success ? 'تم حذف الصورة' : 'فشل في حذف الصورة'),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );
  }

  Future<void> _cleanupImages() async {
    await _imageService.cleanupUnusedImages(_testImages);
    
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تنظيف الصور غير المستخدمة'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
