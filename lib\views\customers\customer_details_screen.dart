import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/customer.dart';
import '../../models/debt.dart';
import '../../services/database/debts_service.dart';
import 'edit_customer_screen.dart';

/// شاشة تفاصيل العميل
class CustomerDetailsScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailsScreen({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen> {
  final DebtsService _debtsService = DebtsService();
  
  List<Debt> _customerDebts = [];
  bool _isLoading = true;
  Map<String, dynamic>? _debtStatistics;

  @override
  void initState() {
    super.initState();
    _loadCustomerDebts();
  }

  /// تحميل مديونيات العميل
  Future<void> _loadCustomerDebts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final debts = await _debtsService.getDebtsByPerson(
        widget.customer.id!,
        AppConstants.personTypeCustomer,
        orderBy: 'date DESC',
      );

      // حساب الإحصائيات
      final statistics = _calculateStatistics(debts);

      setState(() {
        _customerDebts = debts;
        _debtStatistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل المديونيات: $e');
    }
  }

  /// حساب إحصائيات المديونيات
  Map<String, dynamic> _calculateStatistics(List<Debt> debts) {
    double totalAmount = 0;
    double activeAmount = 0;
    double paidAmount = 0;
    int activeCount = 0;
    int paidCount = 0;
    int cancelledCount = 0;

    for (final debt in debts) {
      totalAmount += debt.amount;
      
      switch (debt.status) {
        case AppConstants.debtStatusActive:
          activeAmount += debt.amount;
          activeCount++;
          break;
        case AppConstants.debtStatusPaid:
          paidAmount += debt.amount;
          paidCount++;
          break;
        case AppConstants.debtStatusCancelled:
          cancelledCount++;
          break;
      }
    }

    return {
      'total_amount': totalAmount,
      'active_amount': activeAmount,
      'paid_amount': paidAmount,
      'total_count': debts.length,
      'active_count': activeCount,
      'paid_count': paidCount,
      'cancelled_count': cancelledCount,
    };
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.customer.name),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EditCustomerScreen(
                    customer: widget.customer,
                  ),
                ),
              );
              if (result == true) {
                // تحديث البيانات إذا تم التعديل
                setState(() {});
              }
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  // معلومات العميل الأساسية
                  _buildCustomerInfo(),
                  
                  // إحصائيات المديونيات
                  if (_debtStatistics != null) _buildStatistics(),
                  
                  // قائمة المديونيات
                  _buildDebtsList(),
                ],
              ),
            ),
    );
  }

  /// بناء معلومات العميل
  Widget _buildCustomerInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // أيقونة العميل والاسم
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Center(
                  child: Text(
                    widget.customer.initials,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.customer.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'عميل منذ ${widget.customer.formattedCreatedAt}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // معلومات الاتصال
          if (widget.customer.hasContactInfo) ...[
            const Divider(),
            const SizedBox(height: 16),
            
            if (widget.customer.phone != null && widget.customer.phone!.isNotEmpty)
              _buildInfoRow(Icons.phone, 'الهاتف', widget.customer.phone!),
            
            if (widget.customer.email != null && widget.customer.email!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildInfoRow(Icons.email, 'البريد الإلكتروني', widget.customer.email!),
            ],
            
            if (widget.customer.address != null && widget.customer.address!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildInfoRow(Icons.location_on, 'العنوان', widget.customer.address!),
            ],
          ],
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات المديونيات
  Widget _buildStatistics() {
    final stats = _debtStatistics!;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات المديونيات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              // المديونيات النشطة
              Expanded(
                child: _buildStatCard(
                  'المديونيات النشطة',
                  '${stats['active_amount'].toStringAsFixed(0)} ريال',
                  '${stats['active_count']} مديونية',
                  AppTheme.errorColor,
                  Icons.warning,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // المديونيات المدفوعة
              Expanded(
                child: _buildStatCard(
                  'المديونيات المدفوعة',
                  '${stats['paid_amount'].toStringAsFixed(0)} ريال',
                  '${stats['paid_count']} مديونية',
                  AppTheme.successColor,
                  Icons.check_circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String amount, String count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            amount,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            count,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المديونيات
  Widget _buildDebtsList() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المديونيات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              if (_customerDebts.isNotEmpty)
                Text(
                  '${_customerDebts.length} مديونية',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          if (_customerDebts.isEmpty)
            _buildEmptyDebts()
          else
            AnimationLimiter(
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _customerDebts.length,
                itemBuilder: (context, index) {
                  final debt = _customerDebts[index];
                  
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: _buildDebtCard(debt),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة للمديونيات
  Widget _buildEmptyDebts() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مديونيات',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم تسجيل أي مديونيات لهذا العميل بعد',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المديونية
  Widget _buildDebtCard(Debt debt) {
    Color statusColor;
    IconData statusIcon;
    
    switch (debt.status) {
      case AppConstants.debtStatusActive:
        statusColor = AppTheme.errorColor;
        statusIcon = Icons.warning;
        break;
      case AppConstants.debtStatusPaid:
        statusColor = AppTheme.successColor;
        statusIcon = Icons.check_circle;
        break;
      case AppConstants.debtStatusCancelled:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: المبلغ والحالة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    debt.formattedAmount,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(statusIcon, size: 16, color: statusColor),
                        const SizedBox(width: 4),
                        Text(
                          debt.statusText,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // التاريخ
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    debt.formattedDate,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              
              // الملاحظات (إذا وجدت)
              if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  debt.notes!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
