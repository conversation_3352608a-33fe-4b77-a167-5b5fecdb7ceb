import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

/// نموذج بيانات المديونية
class Debt {
  final int? id;
  final int personId;
  final String personType;
  final double amount;
  final DateTime date;
  final String? notes;
  final String? imagePath;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Debt({
    this.id,
    required this.personId,
    required this.personType,
    required this.amount,
    required this.date,
    this.notes,
    this.imagePath,
    this.status = AppConstants.debtStatusActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء مديونية من Map (من قاعدة البيانات)
  factory Debt.fromMap(Map<String, dynamic> map) {
    return Debt(
      id: map['id'] as int?,
      personId: map['person_id'] as int,
      personType: map['person_type'] as String,
      amount: (map['amount'] as num).toDouble(),
      date: DateTime.parse(map['date'] as String),
      notes: map['notes'] as String?,
      imagePath: map['image_path'] as String?,
      status: map['status'] as String? ?? AppConstants.debtStatusActive,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// تحويل المديونية إلى Map (لقاعدة البيانات)
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'person_id': personId,
      'person_type': personType,
      'amount': amount,
      'date': date.toIso8601String(),
      'notes': notes,
      'image_path': imagePath,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المديونية
  Debt copyWith({
    int? id,
    int? personId,
    String? personType,
    double? amount,
    DateTime? date,
    String? notes,
    String? imagePath,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Debt(
      id: id ?? this.id,
      personId: personId ?? this.personId,
      personType: personType ?? this.personType,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      notes: notes ?? this.notes,
      imagePath: imagePath ?? this.imagePath,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// التحقق من صحة البيانات
  List<String> validate() {
    List<String> errors = [];

    // التحقق من معرف الشخص
    if (personId <= 0) {
      errors.add('يجب اختيار شخص صحيح');
    }

    // التحقق من نوع الشخص
    if (personType != AppConstants.personTypeCustomer &&
        personType != AppConstants.personTypeEmployee) {
      errors.add('نوع الشخص غير صحيح');
    }

    // التحقق من المبلغ
    if (amount <= 0) {
      errors.add('المبلغ يجب أن يكون أكبر من صفر');
    } else if (amount > 999999999) {
      errors.add('المبلغ كبير جداً');
    }

    // التحقق من التاريخ
    if (date.isAfter(DateTime.now().add(const Duration(days: 1)))) {
      errors.add('التاريخ لا يمكن أن يكون في المستقبل');
    }

    // التحقق من الحالة
    if (status != AppConstants.debtStatusActive &&
        status != AppConstants.debtStatusPaid &&
        status != AppConstants.debtStatusCancelled) {
      errors.add('حالة المديونية غير صحيحة');
    }

    // التحقق من الملاحظات
    if (notes != null && notes!.length > 1000) {
      errors.add('الملاحظات يجب أن تكون أقل من 1000 حرف');
    }

    return errors;
  }

  /// التحقق من صحة البيانات (إرجاع true إذا كانت صحيحة)
  bool get isValid => validate().isEmpty;

  /// التحقق من كون المديونية نشطة
  bool get isActive => status == AppConstants.debtStatusActive;

  /// التحقق من كون المديونية مدفوعة
  bool get isPaid => status == AppConstants.debtStatusPaid;

  /// التحقق من كون المديونية ملغاة
  bool get isCancelled => status == AppConstants.debtStatusCancelled;

  /// التحقق من كون الشخص عميل
  bool get isCustomerDebt => personType == AppConstants.personTypeCustomer;

  /// التحقق من كون الشخص موظف
  bool get isEmployeeDebt => personType == AppConstants.personTypeEmployee;

  /// تنسيق المبلغ للعرض
  String get formattedAmount {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return '${formatter.format(amount)} ريال';
  }

  /// تنسيق المبلغ المختصر للعرض
  String get shortFormattedAmount {
    final formatter = NumberFormat('#,##0', 'ar');
    return formatter.format(amount);
  }

  /// تنسيق التاريخ للعرض
  String get formattedDate {
    return DateFormat('dd/MM/yyyy', 'ar').format(date);
  }

  /// تنسيق التاريخ والوقت للعرض
  String get formattedDateTime {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(date);
  }

  /// تنسيق تاريخ الإنشاء للعرض
  String get formattedCreatedAt {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(createdAt);
  }

  /// تنسيق تاريخ التحديث للعرض
  String get formattedUpdatedAt {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(updatedAt);
  }

  /// الحصول على نص الحالة باللغة العربية
  String get statusText {
    switch (status) {
      case AppConstants.debtStatusActive:
        return 'نشطة';
      case AppConstants.debtStatusPaid:
        return 'مدفوعة';
      case AppConstants.debtStatusCancelled:
        return 'ملغاة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على نص نوع الشخص باللغة العربية
  String get personTypeText {
    switch (personType) {
      case AppConstants.personTypeCustomer:
        return 'عميل';
      case AppConstants.personTypeEmployee:
        return 'موظف';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على لون الحالة
  String get statusColor {
    switch (status) {
      case AppConstants.debtStatusActive:
        return 'red';
      case AppConstants.debtStatusPaid:
        return 'green';
      case AppConstants.debtStatusCancelled:
        return 'grey';
      default:
        return 'grey';
    }
  }

  /// الحصول على أيقونة الحالة
  String get statusIcon {
    switch (status) {
      case AppConstants.debtStatusActive:
        return 'warning';
      case AppConstants.debtStatusPaid:
        return 'check_circle';
      case AppConstants.debtStatusCancelled:
        return 'cancel';
      default:
        return 'help';
    }
  }

  /// الحصول على وصف مختصر للمديونية
  String get summary {
    return '$formattedAmount - $formattedDate - $statusText';
  }

  /// التحقق من كون المديونية حديثة (أقل من 30 يوم)
  bool get isRecent {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return date.isAfter(thirtyDaysAgo);
  }

  /// التحقق من كون المديونية قديمة (أكثر من 90 يوم)
  bool get isOld {
    final ninetyDaysAgo = DateTime.now().subtract(const Duration(days: 90));
    return date.isBefore(ninetyDaysAgo);
  }

  /// حساب عدد الأيام منذ إنشاء المديونية
  int get daysSinceCreated {
    return DateTime.now().difference(date).inDays;
  }

  /// التحقق من وجود صورة مرفقة
  bool get hasImage => imagePath != null && imagePath!.isNotEmpty;

  /// الحصول على اسم ملف الصورة
  String? get imageFileName {
    if (!hasImage) return null;
    return imagePath!.split('/').last;
  }

  /// الحصول على امتداد الصورة
  String? get imageExtension {
    if (!hasImage) return null;
    final fileName = imageFileName!;
    final lastDot = fileName.lastIndexOf('.');
    if (lastDot == -1) return null;
    return fileName.substring(lastDot + 1).toLowerCase();
  }

  /// التحقق من صحة تنسيق الصورة
  bool get isValidImageFormat {
    if (!hasImage) return true; // لا توجد صورة = صحيح
    final extension = imageExtension;
    if (extension == null) return false;
    return AppConstants.supportedImageFormats.contains(extension);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Debt &&
        other.id == id &&
        other.personId == personId &&
        other.personType == personType &&
        other.amount == amount &&
        other.date == date &&
        other.notes == notes &&
        other.imagePath == imagePath &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(
        id, personId, personType, amount, date, notes, imagePath, status);
  }

  @override
  String toString() {
    return 'Debt{id: $id, personId: $personId, personType: $personType, amount: $amount, date: $date, notes: $notes, imagePath: $imagePath, status: $status, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  /// تحويل إلى JSON للتصدير
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'person_id': personId,
      'person_type': personType,
      'amount': amount,
      'date': date.toIso8601String(),
      'notes': notes,
      'image_path': imagePath,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء مديونية من JSON
  factory Debt.fromJson(Map<String, dynamic> json) {
    return Debt(
      id: json['id'] as int?,
      personId: json['person_id'] as int,
      personType: json['person_type'] as String,
      amount: (json['amount'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
      notes: json['notes'] as String?,
      imagePath: json['image_path'] as String?,
      status: json['status'] as String? ?? AppConstants.debtStatusActive,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
    );
  }
}
