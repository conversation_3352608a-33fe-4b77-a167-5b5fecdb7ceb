import 'dart:io';
import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../services/image_service.dart';

/// ويدجت اختيار وعرض الصور
class ImagePickerWidget extends StatefulWidget {
  final String? imagePath;
  final Function(String?) onImageChanged;
  final bool enabled;
  final double? width;
  final double? height;

  const ImagePickerWidget({
    super.key,
    this.imagePath,
    required this.onImageChanged,
    this.enabled = true,
    this.width,
    this.height,
  });

  @override
  State<ImagePickerWidget> createState() => _ImagePickerWidgetState();
}

class _ImagePickerWidgetState extends State<ImagePickerWidget> {
  final ImageService _imageService = ImageService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 200,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey[300]!,
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[50],
      ),
      child: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : widget.imagePath != null && widget.imagePath!.isNotEmpty
              ? _buildImageDisplay()
              : _buildImagePicker(),
    );
  }

  /// عرض الصورة المحددة
  Widget _buildImageDisplay() {
    return Stack(
      children: [
        // الصورة
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: GestureDetector(
            onTap: () => _showImageDialog(),
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Image.file(
                File(widget.imagePath!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            size: 48,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 8),
                          Text(
                            'خطأ في تحميل الصورة',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),

        // أزرار التحكم
        if (widget.enabled) ...[
          // زر الحذف
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.delete,
                  color: Colors.white,
                  size: 20,
                ),
                onPressed: _deleteImage,
                tooltip: AppConstants.deleteImage,
              ),
            ),
          ),

          // زر التغيير
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.edit,
                  color: Colors.white,
                  size: 20,
                ),
                onPressed: _showImageSourceDialog,
                tooltip: 'تغيير الصورة',
              ),
            ),
          ),
        ],

        // أيقونة التكبير
        Positioned(
          bottom: 8,
          right: 8,
          child: Container(
            padding: EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.zoom_in,
              color: Colors.white,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }

  /// عرض منتقي الصور
  Widget _buildImagePicker() {
    return InkWell(
      onTap: widget.enabled ? _showImageSourceDialog : null,
      borderRadius: BorderRadius.circular(10),
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate,
              size: 48,
              color: widget.enabled ? AppTheme.primaryColor : Colors.grey,
            ),
            const SizedBox(height: 12),
            Text(
              'إضافة صورة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: widget.enabled ? AppTheme.primaryColor : Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط لاختيار صورة',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار اختيار مصدر الصورة
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'اختيار مصدر الصورة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // زر الكاميرا
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: AppTheme.primaryColor,
                ),
              ),
              title: const Text(AppConstants.takePhoto),
              subtitle: const Text('التقاط صورة جديدة'),
              onTap: () {
                Navigator.pop(context);
                _takePhoto();
              },
            ),

            // زر المعرض
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.photo_library,
                  color: AppTheme.secondaryColor,
                ),
              ),
              title: const Text(AppConstants.chooseFromGallery),
              subtitle: const Text('اختيار من الصور المحفوظة'),
              onTap: () {
                Navigator.pop(context);
                _pickFromGallery();
              },
            ),

            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    setState(() => _isLoading = true);

    try {
      final imagePath = await _imageService.takePhoto();
      if (imagePath != null) {
        widget.onImageChanged(imagePath);
        _showSnackBar(AppConstants.imageAttached, Colors.green);
      }
    } catch (e) {
      _showSnackBar(e.toString(), Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickFromGallery() async {
    setState(() => _isLoading = true);

    try {
      final imagePath = await _imageService.pickFromGallery();
      if (imagePath != null) {
        widget.onImageChanged(imagePath);
        _showSnackBar(AppConstants.imageAttached, Colors.green);
      }
    } catch (e) {
      _showSnackBar(e.toString(), Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// حذف الصورة
  void _deleteImage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(AppConstants.confirmDeleteImage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(AppConstants.cancel),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              // حذف الصورة من التخزين
              if (widget.imagePath != null) {
                await _imageService.deleteImage(widget.imagePath!);
              }

              // تحديث الحالة
              widget.onImageChanged(null);
              _showSnackBar(AppConstants.imageDeleted, Colors.orange);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text(AppConstants.delete),
          ),
        ],
      ),
    );
  }

  /// عرض الصورة في حوار منفصل
  void _showImageDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // الصورة
            Center(
              child: InteractiveViewer(
                child: Image.file(
                  File(widget.imagePath!),
                  fit: BoxFit.contain,
                ),
              ),
            ),

            // زر الإغلاق
            Positioned(
              top: 40,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض رسالة
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
