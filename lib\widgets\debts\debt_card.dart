import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../models/debt.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/debt_image_thumbnail.dart';

/// بطاقة عرض المديونية مع المعلومات الأساسية والصورة
class DebtCard extends StatelessWidget {
  final Debt debt;
  final String? personName;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onChangeStatus;

  const DebtCard({
    super.key,
    required this.debt,
    this.personName,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onChangeStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(debt.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          if (onEdit != null)
            SlidableAction(
              onPressed: (_) => onEdit!(),
              backgroundColor: AppTheme.secondaryColor,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: 'تعديل',
            ),
          if (onChangeStatus != null)
            SlidableAction(
              onPressed: (_) => onChangeStatus!(),
              backgroundColor: debt.isActive ? Colors.green : Colors.orange,
              foregroundColor: Colors.white,
              icon: debt.isActive ? Icons.check : Icons.undo,
              label: debt.isActive ? 'دفع' : 'تراجع',
            ),
          if (onDelete != null)
            SlidableAction(
              onPressed: (_) => onDelete!(),
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              icon: Icons.delete,
              label: 'حذف',
            ),
        ],
      ),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف الأول: المبلغ والحالة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // المبلغ
                    Text(
                      debt.formattedAmount,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: _getStatusColor(),
                      ),
                    ),

                    // حالة المديونية
                    _buildStatusChip(),
                  ],
                ),

                const SizedBox(height: 12),

                // الصف الثاني: اسم الشخص ونوعه
                if (personName != null) ...[
                  Row(
                    children: [
                      Icon(
                        debt.isCustomerDebt ? Icons.person : Icons.badge,
                        size: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          personName!,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // نوع الشخص
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: debt.isCustomerDebt
                              ? AppTheme.secondaryColor.withOpacity(0.1)
                              : Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          debt.isCustomerDebt ? 'عميل' : 'موظف',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: debt.isCustomerDebt
                                ? AppTheme.secondaryColor
                                : Colors.orange[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],

                // الصف الثالث: التاريخ والصورة
                Row(
                  children: [
                    // التاريخ
                    Expanded(
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            debt.formattedDate,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          // مؤشر الصورة
                          if (debt.hasImage) ...[
                            const SizedBox(width: 12),
                            Icon(
                              Icons.image,
                              size: 16,
                              color: AppTheme.primaryColor.withOpacity(0.7),
                            ),
                          ],
                        ],
                      ),
                    ),

                    // الصورة المصغرة
                    if (debt.hasImage)
                      DebtImageThumbnail(
                        imagePath: debt.imagePath,
                        size: 50,
                      ),
                  ],
                ),

                // الملاحظات
                if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.grey[200]!,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.note,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            debt.notes!,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[700],
                              height: 1.3,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // معلومات إضافية للمديونيات القديمة أو الحديثة
                if (debt.isOld || debt.isRecent) ...[
                  const SizedBox(height: 8),
                  _buildAdditionalInfo(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor() {
    switch (debt.status) {
      case 'active':
        return Colors.orange[700]!;
      case 'paid':
        return Colors.green[700]!;
      case 'cancelled':
        return Colors.red[700]!;
      default:
        return AppTheme.textSecondaryColor;
    }
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip() {
    final color = _getStatusColor();
    IconData icon;
    String text;

    switch (debt.status) {
      case 'active':
        icon = Icons.pending;
        text = 'نشطة';
        break;
      case 'paid':
        icon = Icons.check_circle;
        text = 'مدفوعة';
        break;
      case 'cancelled':
        icon = Icons.cancel;
        text = 'ملغاة';
        break;
      default:
        icon = Icons.help;
        text = 'غير محدد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات إضافية
  Widget _buildAdditionalInfo() {
    if (debt.isOld) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning,
              size: 14,
              color: Colors.red[600],
            ),
            const SizedBox(width: 4),
            Text(
              'مديونية قديمة (${debt.daysSinceCreated} يوم)',
              style: TextStyle(
                fontSize: 11,
                color: Colors.red[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } else if (debt.isRecent) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.new_releases,
              size: 14,
              color: Colors.blue[600],
            ),
            const SizedBox(width: 4),
            Text(
              'مديونية حديثة',
              style: TextStyle(
                fontSize: 11,
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
